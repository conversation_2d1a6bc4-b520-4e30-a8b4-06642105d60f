# 中低频因子神经网络预测系统使用说明

## 系统简介

本系统是一个完整的中低频因子神经网络预测系统，专门用于预测加密货币市场的短期价格走势。系统使用120分钟的历史数据来预测未来60分钟的收益率，特别针对震荡行情vs趋势行情的差异进行了优化。

## 核心特性

- 🎯 **精确预测**：120分钟输入预测60分钟收益
- 📊 **智能因子**：24个高级量价因子，自动质量评估
- 🔄 **状态自适应**：根据市场状态（趋势/震荡/波动）调整策略
- 🛡️ **数据安全**：严格的60/20/20时间序列分割，防止未来信息泄露
- 🧠 **深度学习**：多头注意力机制+状态自适应门控网络
- 📈 **全流程**：从数据处理到模型训练的完整管道

## 快速开始

### 1. 环境要求

```bash
# Python 环境
Python >= 3.8

# 必需依赖
pip install torch pandas numpy scikit-learn matplotlib seaborn

# 可选依赖
pip install xgboost  # 用于XGBoost模型比较
pip install qlib     # 用于更多因子库（可选）
```

### 2. 数据准备

数据格式要求：
```python
# 数据必须包含以下列
required_columns = ['open', 'high', 'low', 'close', 'volume']

# 数据索引必须是时间序列
data.index = pd.DatetimeIndex  # 分钟级别时间索引

# 数据示例
                     open    high     low   close    volume
2024-01-01 00:00:00  100.0  102.0   98.0  101.0  10000.0
2024-01-01 00:01:00  101.0  103.0   99.0  102.0  11000.0
...
```

### 3. 基础使用

```python
from qlib_factor_ml_pipeline import *
import pandas as pd

# 加载数据
data = pd.read_pickle('your_data.pkl')

# 创建统一训练管道
pipeline = UnifiedTrainingPipeline(data_path='your_data.pkl')

# 运行完整训练流程
results = pipeline.run_complete_pipeline()

# 查看结果
print(f"预测准确率: {results['metrics']['direction_accuracy']:.2%}")
print(f"相关系数: {results['metrics']['correlation']:.4f}")
```

### 4. 高级使用

#### 4.1 自定义因子生成
```python
# 创建因子生成器
factor_generator = AdvancedFactorGenerator(
    lookback_window=120,  # 历史窗口
    prediction_horizon=60  # 预测周期
)

# 生成因子
factors = factor_generator.generate_all_factors(data)
print(f"生成了 {len(factors.columns)} 个因子")
```

#### 4.2 因子质量评估
```python
# 创建目标变量
target = data['close'].pct_change(60).shift(-60) * 100

# 评估因子质量
assessor = FactorQualityAssessment(
    min_ic_threshold=0.02,
    min_win_rate_threshold=0.52
)

# 选择最佳因子
best_factors = assessor.select_best_factors(factors, target, top_n=20)
print(f"选择了 {len(best_factors)} 个高质量因子")
```

#### 4.3 市场状态检测
```python
# 创建状态检测器
detector = MarketStateDetector(
    trend_threshold=0.6,
    volatility_threshold=0.3
)

# 检测市场状态
price_120min = data['close'].iloc[-120:].values
state = detector.detect_detailed_market_state(price_120min)
print(f"当前市场状态: {state['basic_state']}")
```

#### 4.4 神经网络训练
```python
# 创建状态自适应网络
model = StateAdaptiveNetwork(
    input_size=24,  # 因子数量
    hidden_size=128,
    num_heads=8,
    num_layers=3,
    dropout=0.15
)

# 创建训练器
trainer = StateAdaptiveTrainer(model)

# 训练模型
trainer.train_model(train_loader, val_loader, epochs=200)
```

## 模块详解

### 1. TimeSeriesSafeDataSplitter
严格的时间序列数据分割器，确保训练/验证/测试集按时间顺序分割。

```python
splitter = TimeSeriesSafeDataSplitter(
    train_ratio=0.6,
    val_ratio=0.2,
    test_ratio=0.2
)

data_splits = splitter.split_data(data)
```

### 2. AdvancedFactorGenerator
高级因子生成器，生成4大类24个因子：
- 微观结构因子：价量弹性、流动性指纹、订单流失衡等
- 时序动量因子：分段动量、动量加速度、趋势持续性
- 价量关系因子：价量相关性、VWAP偏离、价量同步性
- 波动率因子：已实现波动率、波动率偏度、波动率聚类

### 3. FactorQualityAssessment
因子质量评估系统，包含：
- IC值计算（信息系数）
- 胜率计算
- 滚动IC稳定性
- 因子衰减分析
- 自动因子筛选

### 4. MarketStateDetector
市场状态检测器，识别：
- 基础状态：trending（趋势） / ranging（震荡）
- 趋势方向：up / down / neutral
- 波动率水平：数值化波动率
- 动量强度：价格动量测量
- 反转概率：基于价格位置的反转概率

### 5. StateAdaptiveNetwork
状态自适应神经网络，特点：
- 多头注意力机制
- 状态自适应门控
- 因子特征融合
- 端到端训练

### 6. RollingDataAugmenter
滚动数据增强器，仅对训练集进行增强：
- 滑动窗口增强
- 市场状态平衡
- 数据质量保证

## 性能评估

### 评估指标
- **方向准确率**：预测方向的正确率
- **信息系数(IC)**：预测值与实际值的相关性
- **胜率**：盈利交易占总交易的比例
- **最大回撤**：最大亏损幅度
- **夏普比率**：风险调整后收益

### 目标性能
- 方向准确率：>55%
- 信息比率：>1.5
- 最大回撤：<15%
- 预测稳定性：IC月度标准差<0.3

## 配置说明

### 默认配置
```python
DEFAULT_CONFIG = {
    'data_split': {
        'train_ratio': 0.6,
        'val_ratio': 0.2,
        'test_ratio': 0.2
    },
    'factor_generation': {
        'lookback_window': 120,
        'prediction_horizon': 60
    },
    'factor_selection': {
        'min_ic_threshold': 0.02,
        'min_win_rate_threshold': 0.52,
        'max_factors': 30
    },
    'model': {
        'hidden_size': 128,
        'num_heads': 8,
        'num_layers': 3,
        'dropout': 0.15,
        'learning_rate': 0.0003,
        'batch_size': 64,
        'max_epochs': 200,
        'patience': 30
    }
}
```

### 自定义配置
```python
# 修改配置
custom_config = DEFAULT_CONFIG.copy()
custom_config['model']['hidden_size'] = 256
custom_config['model']['num_heads'] = 16

# 使用自定义配置
pipeline = UnifiedTrainingPipeline(
    data_path='your_data.pkl',
    config=custom_config
)
```

## 故障排除

### 常见问题

1. **数据格式错误**
   ```
   错误: KeyError: 'close'
   解决: 确保数据包含 ['open', 'high', 'low', 'close', 'volume'] 列
   ```

2. **内存不足**
   ```
   错误: RuntimeError: CUDA out of memory
   解决: 减少batch_size或使用CPU训练
   ```

3. **数据量不足**
   ```
   错误: ValueError: 数据量过少
   解决: 确保数据量>5000行，且包含完整的时间序列
   ```

4. **因子全为NaN**
   ```
   错误: 因子计算失败
   解决: 检查数据质量，确保价格数据连续且合理
   ```

### 日志和调试
```python
# 开启详细日志
import logging
logging.basicConfig(level=logging.INFO)

# 调试模式
pipeline = UnifiedTrainingPipeline(
    data_path='your_data.pkl',
    debug=True
)
```

## 扩展开发

### 添加新因子
```python
class CustomFactorGenerator(AdvancedFactorGenerator):
    def generate_custom_factors(self, data):
        factors = {}
        
        # 添加你的自定义因子
        factors['custom_factor'] = data['close'].rolling(20).mean()
        
        return factors
```

### 添加新的市场状态
```python
class CustomMarketStateDetector(MarketStateDetector):
    def detect_custom_state(self, price_data):
        # 添加自定义状态检测逻辑
        return 'custom_state'
```

### 自定义损失函数
```python
class CustomLoss(nn.Module):
    def forward(self, pred, target):
        # 实现自定义损失函数
        return loss
```

## 更新日志

### v1.0.0 (2024-12-29)
- ✅ 完整的端到端训练流程
- ✅ 24个高级因子生成
- ✅ 状态自适应神经网络
- ✅ 自动因子质量评估
- ✅ 市场状态检测
- ✅ 滚动数据增强

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题或建议，请参考：
- 代码文档：注释详细的源代码
- 优化建议：`optimization_recommendations.md`
- 测试用例：集成测试代码

---

**注意**：本系统用于学术研究和教育目的，不构成投资建议。实际使用时请谨慎评估风险。 