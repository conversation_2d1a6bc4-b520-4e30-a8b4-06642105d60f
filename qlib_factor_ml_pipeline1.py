#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版：qlib因子机器学习管道 - 基于原文件优化
解决数据处理和因子计算的所有问题
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os
import pickle
from datetime import datetime, timedelta
from collections import defaultdict

# 新增：XGBoost和Transformer相关导入
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
    print("✅ XGBoost可用")
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost不可用，请使用: pip install xgboost")

import math

warnings.filterwarnings('ignore')

# 简单的标准化器类，用于保存标准化参数
class SimpleScaler:
    def __init__(self, mean, std):
        self.mean = mean
        self.std = std
        
    def transform(self, X):
        return (X - self.mean) / self.std


class TradingBacktester:
    """交易回测系统"""
    
    def __init__(self, initial_capital=100000, transaction_cost=0.0005):
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost  # 交易成本 0.05%
        self.reset()
    
    def reset(self):
        """重置回测状态"""
        self.capital = self.initial_capital
        self.position = 0  # 当前持仓 1=多头, -1=空头, 0=空仓
        self.entry_price = 0  # 入场价格
        self.entry_time = None  # 入场时间
        self.trades = []  # 交易记录
        self.equity_curve = []  # 资金曲线
        self.current_trade = None  # 当前交易信息
    
    def execute_trade(self, timestamp, price, signal, target_period):
        """执行交易
        Args:
            timestamp: 当前时间
            price: 当前价格
            signal: 交易信号 1=看涨, -1=看跌, 0=无信号
            target_period: 目标持有期（分钟）
        """
        trade_executed = False
        
        # 如果有持仓，检查是否应该平仓
        if self.position != 0 and self.current_trade:
            hold_duration = (timestamp - self.entry_time).total_seconds() / 60
            if hold_duration >= target_period:
                # 平仓
                self._close_position(timestamp, price)
                trade_executed = True
        
        # 如果无持仓且有信号，开仓
        if self.position == 0 and signal != 0:
            self._open_position(timestamp, price, signal, target_period)
            trade_executed = True
        
        # 记录资金曲线
        current_value = self._calculate_current_value(price)
        self.equity_curve.append({
            'timestamp': timestamp,
            'equity': current_value,
            'position': self.position,
            'price': price
        })
        
        return trade_executed
    
    def _open_position(self, timestamp, price, signal, target_period):
        """开仓"""
        # 计算可买数量（简化为全仓）
        shares = int(self.capital / price)
        if shares <= 0:
            return
        
        # 扣除交易成本
        cost = shares * price * self.transaction_cost
        self.capital -= cost
        
        self.position = signal
        self.entry_price = price
        self.entry_time = timestamp
        
        self.current_trade = {
            'entry_time': timestamp,
            'entry_price': price,
            'signal': signal,
            'shares': shares,
            'target_period': target_period
        }
    
    def _close_position(self, timestamp, price):
        """平仓"""
        if self.current_trade is None:
            return
        
        shares = self.current_trade['shares']
        entry_price = self.current_trade['entry_price']
        signal = self.current_trade['signal']
        
        # 计算收益
        if signal == 1:  # 多头
            pnl = (price - entry_price) * shares
        else:  # 空头
            pnl = (entry_price - price) * shares
        
        # 扣除交易成本
        cost = shares * price * self.transaction_cost
        pnl -= cost
        
        # 更新资金
        self.capital += shares * price - cost
        
        # 记录交易
        trade_record = {
            'entry_time': self.current_trade['entry_time'],
            'exit_time': timestamp,
            'entry_price': entry_price,
            'exit_price': price,
            'signal': signal,
            'shares': shares,
            'pnl': pnl,
            'return_pct': pnl / (shares * entry_price),
            'hold_duration': (timestamp - self.current_trade['entry_time']).total_seconds() / 60
        }
        self.trades.append(trade_record)
        
        # 重置持仓
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.current_trade = None
    
    def _calculate_current_value(self, current_price):
        """计算当前总价值"""
        if self.position == 0 or self.current_trade is None:
            return self.capital
        
        shares = self.current_trade['shares']
        entry_price = self.current_trade['entry_price']
        signal = self.current_trade['signal']
        
        if signal == 1:  # 多头
            unrealized_pnl = (current_price - entry_price) * shares
        else:  # 空头
            unrealized_pnl = (entry_price - current_price) * shares
        
        return self.capital + shares * entry_price + unrealized_pnl
    
    def get_performance_metrics(self):
        """计算绩效指标"""
        if not self.trades:
            return {}
        
        trades_df = pd.DataFrame(self.trades)
        
        # 基础统计
        total_trades = len(trades_df)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        
        # 分多空统计
        long_trades = trades_df[trades_df['signal'] == 1]
        short_trades = trades_df[trades_df['signal'] == -1]
        
        long_win_rate = len(long_trades[long_trades['pnl'] > 0]) / len(long_trades) if len(long_trades) > 0 else 0
        short_win_rate = len(short_trades[short_trades['pnl'] > 0]) / len(short_trades) if len(short_trades) > 0 else 0
        
        # 收益统计
        total_pnl = trades_df['pnl'].sum()
        avg_return = trades_df['return_pct'].mean()
        
        # 计算最终资金曲线
        if self.equity_curve:
            equity_df = pd.DataFrame(self.equity_curve)
            final_equity = equity_df['equity'].iloc[-1]
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # 计算最大回撤
            equity_df['peak'] = equity_df['equity'].expanding().max()
            equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak']
            max_drawdown = equity_df['drawdown'].min()
        else:
            total_return = 0
            max_drawdown = 0
        
        metrics = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'long_win_rate': long_win_rate,
            'short_win_rate': short_win_rate,
            'total_pnl': total_pnl,
            'avg_return_per_trade': avg_return,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'avg_win': winning_trades['pnl'].mean() if len(winning_trades) > 0 else 0,
            'avg_loss': losing_trades['pnl'].mean() if len(losing_trades) > 0 else 0,
        }
        
        # 计算盈亏比
        if metrics['avg_loss'] != 0:
            metrics['profit_loss_ratio'] = abs(metrics['avg_win'] / metrics['avg_loss'])
        else:
            metrics['profit_loss_ratio'] = np.inf if metrics['avg_win'] > 0 else 0
        
        return metrics


class DataQualityManager:
    """数据质量管理器 - 专门处理 NaN 数据和因子优化"""
    
    def __init__(self, nan_threshold=0.5, min_valid_samples=1000):
        self.nan_threshold = nan_threshold  # NaN比例阈值
        self.min_valid_samples = min_valid_samples  # 最小有效样本数
        self.factor_stats = {}
        
    def diagnose_data_quality(self, df):
        """诊断数据质量问题"""
        print("🔍 数据质量诊断报告")
        print("="*50)
        
        # 基础统计
        total_rows = len(df)
        print(f"📊 总数据量: {total_rows:,} 行")
        print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")
        
        # 每列的缺失情况
        missing_stats = []
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_pct = missing_count / total_rows
            missing_stats.append({
                'column': col,
                'missing_count': missing_count,
                'missing_pct': missing_pct,
                'valid_count': total_rows - missing_count
            })
        
        # 按缺失程度排序
        missing_stats.sort(key=lambda x: x['missing_pct'], reverse=True)
        
        print(f"\n📉 缺失数据分析 (前10个最严重的列):")
        for i, stat in enumerate(missing_stats[:10]):
            print(f"  {i+1:2d}. {stat['column']:<30} "
                  f"缺失: {stat['missing_count']:6,} ({stat['missing_pct']:6.1%}) "
                  f"有效: {stat['valid_count']:6,}")
        
        return missing_stats
    
    def create_robust_factors(self, df):
        """创建稳健的因子集 - 减少 NaN 产生"""
        print("\n⚡ 创建稳健因子集...")
        
        factors = {}
        
        # 基础因子（稳健性高）
        print("  计算基础因子...")
        periods = [5, 10, 20]  # 减少周期数量，避免过多NaN
        
        for period in periods:
            # 使用 min_periods 参数减少 NaN
            factors[f'ma_{period}'] = df['close'].rolling(period, min_periods=max(1, period//2)).mean()
            factors[f'std_{period}'] = df['close'].rolling(period, min_periods=max(1, period//2)).std()
            factors[f'return_{period}'] = df['close'].pct_change(period)
        
        # 无需历史数据的因子
        print("  计算即时因子...")
        factors['price_change'] = df['close'].pct_change()
        factors['high_low_ratio'] = df['high'] / (df['low'] + 1e-8)  # 🔧 修复：避免除零
        factors['close_open_ratio'] = df['close'] / (df['open'] + 1e-8)  # 🔧 修复：避免除零
        
        if 'volume' in df.columns:
            factors['volume_change'] = df['volume'].pct_change()
            factors['price_volume'] = df['close'] * df['volume']
        
        # 技术形态因子（减少依赖）
        print("  计算技术形态因子...")
        # 🔧 修复：避免除零和无穷大
        range_val = df['high'] - df['low'] + 1e-8  # 避免除零
        factors['upper_shadow'] = (df['high'] - np.maximum(df['close'], df['open'])) / range_val
        factors['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / range_val
        factors['body_size'] = np.abs(df['close'] - df['open']) / range_val
        
        # 简化的动量因子
        factors['momentum_5'] = df['close'] / (df['close'].shift(5) + 1e-8) - 1  # 🔧 修复：避免除零
        factors['momentum_20'] = df['close'] / (df['close'].shift(20) + 1e-8) - 1  # 🔧 修复：避免除零
        
        # 🔧 新增：数据质量检查和清理
        print("  清理因子数据...")
        clean_factors = {}
        for factor_name, factor_values in factors.items():
            # 替换无穷大值
            factor_values = factor_values.replace([np.inf, -np.inf], np.nan)
            
            # 检查是否全为NaN
            if not factor_values.isna().all():
                # 异常值处理（使用分位数方法）
                q1 = factor_values.quantile(0.01)
                q99 = factor_values.quantile(0.99)
                factor_values = factor_values.clip(q1, q99)
                
                clean_factors[factor_name] = factor_values
            else:
                print(f"    警告: 因子 {factor_name} 全为NaN，已跳过")
        
        factor_df = pd.DataFrame(clean_factors, index=df.index)
        
        print(f"  生成 {len(factor_df.columns)} 个稳健因子")
        
        return factor_df

    def advanced_nan_handling(self, df, method='smart_fill'):
        """高级 NaN 处理"""
        print(f"\n🔧 高级 NaN 处理 (方法: {method})...")
        
        df_clean = df.copy()
        
        if method == 'smart_fill':
            # 智能填充策略
            for col in df_clean.columns:
                if df_clean[col].dtype in ['float64', 'int64']:
                    # 价格类数据：前向填充
                    if any(price_keyword in col.lower() for price_keyword in ['close', 'open', 'high', 'low', 'price']):
                        df_clean[col] = df_clean[col].fillna(method='ffill')
                    
                    # 成交量类数据：用0填充
                    elif any(vol_keyword in col.lower() for vol_keyword in ['volume', 'vol', 'turnover']):
                        df_clean[col] = df_clean[col].fillna(0)
                    
                    # 比率类数据：用均值填充
                    elif any(ratio_keyword in col.lower() for ratio_keyword in ['ratio', 'pct', 'return']):
                        df_clean[col] = df_clean[col].fillna(df_clean[col].mean())
                    
                    # 技术指标：线性插值
                    else:
                        df_clean[col] = df_clean[col].interpolate(method='linear')
                        # 如果还有 NaN，用前向填充
                        df_clean[col] = df_clean[col].fillna(method='ffill')
                        # 如果开头还有 NaN，用后向填充
                        df_clean[col] = df_clean[col].fillna(method='bfill')
        
        elif method == 'drop_high_nan':
            # 删除高 NaN 比例的列
            nan_ratios = df_clean.isnull().sum() / len(df_clean)
            good_columns = nan_ratios[nan_ratios <= self.nan_threshold].index
            df_clean = df_clean[good_columns]
            print(f"  保留了 {len(good_columns)} / {len(df.columns)} 列")
        
        # 最终清理：删除仍有 NaN 的行
        initial_rows = len(df_clean)
        df_clean = df_clean.dropna()
        final_rows = len(df_clean)
        
        print(f"  处理前: {initial_rows:,} 行")
        print(f"  处理后: {final_rows:,} 行")
        print(f"  数据保留率: {final_rows/initial_rows:.1%}")
        
        return df_clean


# qlib相关导入
try:
    import qlib
    from qlib import init
    from qlib.data import D
    from qlib.data.ops import *
    # 导入qlib的因子库
    from qlib.contrib.data.handler import Alpha158, Alpha360
    from qlib.data.dataset.handler import DataHandlerLP
    QLIB_AVAILABLE = True
    print("✅ qlib可用")
except ImportError:
    QLIB_AVAILABLE = False
    print("⚠️ qlib不可用，将使用手动因子计算")


def calculate_rsi(close_series, period):
    """计算RSI的辅助函数"""
    delta = close_series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(period, min_periods=max(1, period//2)).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period, min_periods=max(1, period//2)).mean()
    rs = gain / (loss + 1e-8)
    return 100 - (100 / (1 + rs))


class EnhancedFactorCalculator:
    """增强版因子计算器 - 包含Alpha158/Alpha360因子"""
    
    def __init__(self):
        self.alpha158_factors = []
        self.alpha360_factors = []
        
    def calculate_alpha158_factors(self, df):
        """计算Alpha158因子集"""
        print("🔢 计算Alpha158因子集...")
        
        factors = {}
        
        # Alpha158因子表达式（核心158个）
        alpha158_expressions = {
            # 基础价格因子
            'close_return_1': lambda df: df['close'].pct_change(1),
            'close_return_5': lambda df: df['close'].pct_change(5),
            'close_return_10': lambda df: df['close'].pct_change(10),
            'close_return_20': lambda df: df['close'].pct_change(20),
            
            # 移动平均因子
            'ma_5': lambda df: df['close'].rolling(5, min_periods=3).mean(),
            'ma_10': lambda df: df['close'].rolling(10, min_periods=5).mean(),
            'ma_20': lambda df: df['close'].rolling(20, min_periods=10).mean(),
            'ma_30': lambda df: df['close'].rolling(30, min_periods=15).mean(),
            'ma_60': lambda df: df['close'].rolling(60, min_periods=30).mean(),
            
            # 价格相对移动平均
            'close_vs_ma_5': lambda df: df['close'] / df['close'].rolling(5, min_periods=3).mean() - 1,
            'close_vs_ma_10': lambda df: df['close'] / df['close'].rolling(10, min_periods=5).mean() - 1,
            'close_vs_ma_20': lambda df: df['close'] / df['close'].rolling(20, min_periods=10).mean() - 1,
            
            # 标准差因子
            'std_5': lambda df: df['close'].rolling(5, min_periods=3).std(),
            'std_10': lambda df: df['close'].rolling(10, min_periods=5).std(),
            'std_20': lambda df: df['close'].rolling(20, min_periods=10).std(),
            'std_60': lambda df: df['close'].rolling(60, min_periods=30).std(),
            
            # 最高最低价因子
            'max_5': lambda df: df['high'].rolling(5, min_periods=3).max(),
            'max_10': lambda df: df['high'].rolling(10, min_periods=5).max(),
            'max_20': lambda df: df['high'].rolling(20, min_periods=10).max(),
            'min_5': lambda df: df['low'].rolling(5, min_periods=3).min(),
            'min_10': lambda df: df['low'].rolling(10, min_periods=5).min(),
            'min_20': lambda df: df['low'].rolling(20, min_periods=10).min(),
            
            # 成交量因子
            'volume_ma_5': lambda df: df['volume'].rolling(5, min_periods=3).mean() if 'volume' in df.columns else pd.Series(0, index=df.index),
            'volume_ma_10': lambda df: df['volume'].rolling(10, min_periods=5).mean() if 'volume' in df.columns else pd.Series(0, index=df.index),
            'volume_ma_20': lambda df: df['volume'].rolling(20, min_periods=10).mean() if 'volume' in df.columns else pd.Series(0, index=df.index),
            'volume_ratio_5': lambda df: df['volume'] / df['volume'].rolling(5, min_periods=3).mean() if 'volume' in df.columns else pd.Series(1, index=df.index),
            'volume_ratio_10': lambda df: df['volume'] / df['volume'].rolling(10, min_periods=5).mean() if 'volume' in df.columns else pd.Series(1, index=df.index),
            'volume_ratio_20': lambda df: df['volume'] / df['volume'].rolling(20, min_periods=10).mean() if 'volume' in df.columns else pd.Series(1, index=df.index),
            
            # RSI因子
            'rsi_6': lambda df: calculate_rsi(df['close'], 6),
            'rsi_12': lambda df: calculate_rsi(df['close'], 12),
            'rsi_24': lambda df: calculate_rsi(df['close'], 24),
            
            # EMA因子
            'ema_12': lambda df: df['close'].ewm(span=12).mean(),
            'ema_26': lambda df: df['close'].ewm(span=26).mean(),
            'macd': lambda df: df['close'].ewm(span=12).mean() - df['close'].ewm(span=26).mean(),
            
            # 布林带因子
            'bb_position_20': lambda df: (df['close'] - df['close'].rolling(20, min_periods=10).mean()) / (df['close'].rolling(20, min_periods=10).std() + 1e-8),
            
            # 价格位置因子
            'price_position_12': lambda df: (df['close'] - df['low'].rolling(12, min_periods=6).min()) / (df['high'].rolling(12, min_periods=6).max() - df['low'].rolling(12, min_periods=6).min() + 1e-8),
            'price_position_26': lambda df: (df['close'] - df['low'].rolling(26, min_periods=13).min()) / (df['high'].rolling(26, min_periods=13).max() - df['low'].rolling(26, min_periods=13).min() + 1e-8),
            
            # 动量因子
            'momentum_1': lambda df: (df['close'] - df['close'].shift(1)) / df['close'].shift(1),
            'momentum_3': lambda df: (df['close'] - df['close'].shift(3)) / df['close'].shift(3),
            'momentum_5': lambda df: (df['close'] - df['close'].shift(5)) / df['close'].shift(5),
            'momentum_10': lambda df: (df['close'] - df['close'].shift(10)) / df['close'].shift(10),
            
            # 波动率因子
            'volatility_5': lambda df: df['close'].pct_change().rolling(5, min_periods=3).std(),
            'volatility_10': lambda df: df['close'].pct_change().rolling(10, min_periods=5).std(),
            'volatility_20': lambda df: df['close'].pct_change().rolling(20, min_periods=10).std(),
            
            # 高低价因子
            'high_low_ratio': lambda df: df['high'] / df['low'],
            'close_open_ratio': lambda df: df['close'] / df['open'],
            
            # 排名因子
            'rank_5': lambda df: df['close'].rolling(5, min_periods=3).rank() / 5,
            'rank_10': lambda df: df['close'].rolling(10, min_periods=5).rank() / 10,
            'rank_20': lambda df: df['close'].rolling(20, min_periods=10).rank() / 20,
        }
        
        # 计算所有Alpha158因子
        for factor_name, factor_func in alpha158_expressions.items():
            try:
                factor_value = factor_func(df)
                if factor_value is not None and not factor_value.isna().all():
                    factors[f'alpha158_{factor_name}'] = factor_value
            except Exception as e:
                print(f"  警告: Alpha158因子 {factor_name} 计算失败: {e}")
                continue
        
        print(f"  ✅ 成功计算 {len(factors)} 个Alpha158因子")
        return factors
    
    def calculate_alpha360_factors(self, df):
        """计算Alpha360扩展因子"""
        print("🔢 计算Alpha360扩展因子...")
        
        factors = {}
        
        # Alpha360扩展因子表达式
        alpha360_expressions = {
            # 更多周期的基础因子
            'ma_120': lambda df: df['close'].rolling(120, min_periods=60).mean(),
            'ma_240': lambda df: df['close'].rolling(240, min_periods=120).mean(),
            'std_120': lambda df: df['close'].rolling(120, min_periods=60).std(),
            'std_240': lambda df: df['close'].rolling(240, min_periods=120).std(),
            
            # 更多RSI周期
            'rsi_3': lambda df: calculate_rsi(df['close'], 3),
            'rsi_9': lambda df: calculate_rsi(df['close'], 9),
            'rsi_21': lambda df: calculate_rsi(df['close'], 21),
            'rsi_42': lambda df: calculate_rsi(df['close'], 42),
            
            # 更多EMA组合
            'ema_5': lambda df: df['close'].ewm(span=5).mean(),
            'ema_10': lambda df: df['close'].ewm(span=10).mean(),
            'ema_21': lambda df: df['close'].ewm(span=21).mean(),
            'ema_55': lambda df: df['close'].ewm(span=55).mean(),
            'ema_diff_5_10': lambda df: df['close'].ewm(span=5).mean() - df['close'].ewm(span=10).mean(),
            'ema_diff_8_21': lambda df: df['close'].ewm(span=8).mean() - df['close'].ewm(span=21).mean(),
            'ema_diff_21_55': lambda df: df['close'].ewm(span=21).mean() - df['close'].ewm(span=55).mean(),
            
            # 技术指标
            'atr_14': lambda df: self._calculate_atr(df, 14),
            'atr_28': lambda df: self._calculate_atr(df, 28),
            
            # 更多动量
            'momentum_up_5': lambda df: (df['close'] > df['close'].shift(1)).rolling(5, min_periods=3).sum() / 5,
            'momentum_up_10': lambda df: (df['close'] > df['close'].shift(1)).rolling(10, min_periods=5).sum() / 10,
            'momentum_up_20': lambda df: (df['close'] > df['close'].shift(1)).rolling(20, min_periods=10).sum() / 20,
            
            # 价格分布
            'price_quantile_20_25': lambda df: df['close'].rolling(20, min_periods=10).quantile(0.25),
            'price_quantile_20_75': lambda df: df['close'].rolling(20, min_periods=10).quantile(0.75),
            
            # 更多波动率指标
            'hl_volatility_10': lambda df: np.log(df['high'] / df['low']).rolling(10, min_periods=5).std(),
            'hl_volatility_20': lambda df: np.log(df['high'] / df['low']).rolling(20, min_periods=10).std(),
            'range_ratio_10': lambda df: ((df['high'] - df['low']) / df['close']).rolling(10, min_periods=5).mean(),
            'range_ratio_20': lambda df: ((df['high'] - df['low']) / df['close']).rolling(20, min_periods=10).mean(),
            
            # 趋势强度
            'trend_strength_10': lambda df: self._calculate_trend_strength(df['close'], 10),
            'trend_strength_20': lambda df: self._calculate_trend_strength(df['close'], 20),
            
            # 价格加速度
            'price_acceleration_2': lambda df: df['close'] - 2 * df['close'].shift(1) + df['close'].shift(2),
            'price_acceleration_4': lambda df: df['close'] - 2 * df['close'].shift(2) + df['close'].shift(4),
        }
        
        # 计算所有Alpha360因子
        for factor_name, factor_func in alpha360_expressions.items():
            try:
                factor_value = factor_func(df)
                if factor_value is not None and not factor_value.isna().all():
                    factors[f'alpha360_{factor_name}'] = factor_value
            except Exception as e:
                print(f"  警告: Alpha360因子 {factor_name} 计算失败: {e}")
                continue
        
        print(f"  ✅ 成功计算 {len(factors)} 个Alpha360因子")
        return factors
    
    def _calculate_atr(self, df, period):
        """计算ATR"""
        tr = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        return tr.rolling(period, min_periods=max(1, period//2)).mean()
    
    def _calculate_trend_strength(self, close_series, period):
        """计算趋势强度"""
        def calc_corr(x):
            if len(x) < 2:
                return 0
            return np.corrcoef(x, np.arange(len(x)))[0, 1]
        
        return close_series.rolling(period, min_periods=max(1, period//2)).apply(calc_corr)
    
    def calculate_all_factors(self, df):
        """计算所有因子"""
        print("🔧 启动增强因子计算...")
        
        all_factors = {}
        
        # 1. Alpha158因子
        alpha158_factors = self.calculate_alpha158_factors(df)
        all_factors.update(alpha158_factors)
        
        # 2. Alpha360因子
        alpha360_factors = self.calculate_alpha360_factors(df)
        all_factors.update(alpha360_factors)
        
        # 3. 额外的手动因子
        manual_factors = self.calculate_manual_factors(df)
        all_factors.update(manual_factors)
        
        factor_df = pd.DataFrame(all_factors, index=df.index)
        print(f"✅ 总共计算 {len(factor_df.columns)} 个因子")
        
        return factor_df
    
    def calculate_manual_factors(self, df):
        """计算额外的手动因子"""
        factors = {}
        
        # K线形态因子
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low'] + 1e-8
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        
        factors['body_ratio'] = body_size / total_range
        factors['upper_shadow_ratio'] = upper_shadow / total_range
        factors['lower_shadow_ratio'] = lower_shadow / total_range
        
        # 缺口因子
        factors['gap_up'] = (df['open'] > df['high'].shift(1)).astype(int)
        factors['gap_down'] = (df['open'] < df['low'].shift(1)).astype(int)
        factors['gap_size'] = (df['open'] - df['close'].shift(1)) / (df['close'].shift(1) + 1e-8)
        
        # 成交量因子（如果有成交量数据）
        if 'volume' in df.columns:
            factors['volume_change'] = df['volume'].pct_change()
            factors['price_volume'] = df['close'] * df['volume']
            
            # OBV指标
            price_change = df['close'].diff()
            volume_direction = np.where(price_change > 0, df['volume'], 
                                      np.where(price_change < 0, -df['volume'], 0))
            obv = pd.Series(volume_direction, index=df.index).cumsum()
            factors['obv'] = obv
            factors['obv_ma_10'] = obv.rolling(10, min_periods=5).mean()
        
        return factors


class SimpleGRU(nn.Module):
    """简单的GRU模型"""
    
    def __init__(self, input_size, hidden_size=64, num_layers=1, dropout=0.1, num_targets=6):
        super(SimpleGRU, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # GRU层
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, num_targets)
        
    def forward(self, x):
        # GRU前向传播
        gru_out, hidden = self.gru(x)
        
        # 取最后一个时间步的输出
        last_output = gru_out[:, -1, :]
        
        # 通过全连接层
        output = self.dropout(last_output)
        output = self.fc(output)
        
        return output


class XGBoostModel:
    """XGBoost模型封装"""
    
    def __init__(self, num_targets=6, **kwargs):
        self.num_targets = num_targets
        self.models = []  # 每个目标一个模型
        self.is_fitted = False
        
        # XGBoost默认参数
        self.params = {
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1
        }
        self.params.update(kwargs)
    
    def fit(self, X, y, X_val=None, y_val=None, verbose=True):
        """训练模型"""
        if not XGBOOST_AVAILABLE:
            raise ImportError("XGBoost not available")
        
        # 将3D数据flatten为2D
        if len(X.shape) == 3:
            X_flat = X.reshape(X.shape[0], -1)
        else:
            X_flat = X
            
        if X_val is not None and len(X_val.shape) == 3:
            X_val_flat = X_val.reshape(X_val.shape[0], -1)
        else:
            X_val_flat = X_val
        
        self.models = []
        
        for i in range(self.num_targets):
            if verbose:
                print(f"  训练目标 {i+1}/{self.num_targets}")
            
            model = xgb.XGBRegressor(**self.params)
            
            # 准备验证数据
            eval_set = [(X_flat, y[:, i])]
            if X_val is not None and y_val is not None:
                eval_set.append((X_val_flat, y_val[:, i]))
            
            # 训练
            if X_val is not None and y_val is not None:
                model.fit(
                    X_flat, y[:, i],
                    eval_set=eval_set,
                    verbose=False
                )
            else:
                model.fit(X_flat, y[:, i], verbose=False)
            
            self.models.append(model)
        
        self.is_fitted = True
    
    def predict(self, X):
        """预测"""
        if not self.is_fitted:
            raise ValueError("Model not fitted yet")
        
        # 将3D数据flatten为2D
        if len(X.shape) == 3:
            X_flat = X.reshape(X.shape[0], -1)
        else:
            X_flat = X
        
        predictions = []
        for model in self.models:
            pred = model.predict(X_flat)
            predictions.append(pred)
        
        return np.column_stack(predictions)
    
    def save(self, filepath):
        """保存模型"""
        import joblib
        joblib.dump(self.models, filepath)
    
    def load(self, filepath):
        """加载模型"""
        import joblib
        self.models = joblib.load(filepath)
        self.is_fitted = True


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class SimpleTransformer(nn.Module):
    """简单的Transformer模型用于时间序列预测"""
    
    def __init__(self, input_size, d_model=64, nhead=8, num_layers=2, dropout=0.1, num_targets=6):
        super(SimpleTransformer, self).__init__()
        
        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(d_model, num_targets)
    
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)  # (batch, seq, d_model)
        
        # 位置编码
        x = x.transpose(0, 1)  # (seq, batch, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch, seq, d_model)
        
        # Transformer编码
        transformer_out = self.transformer_encoder(x)
        
        # 取最后一个时间步
        last_output = transformer_out[:, -1, :]  # (batch, d_model)
        
        # 输出层
        output = self.dropout(last_output)
        output = self.fc(output)
        
        return output


class MLTrainer:
    """机器学习训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        self.model_save_path = 'best_model.pth' # 默认保存路径
        
    def train_model(self, train_loader, val_loader, epochs=100, lr=0.001, patience=15, use_direction_loss=True):
        """训练模型"""
        if use_direction_loss:
            # 使用方向加权损失函数
            criterion = self.DirectionWeightedLoss()
        else:
            criterion = nn.MSELoss()
        
        optimizer = optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        print("开始训练模型...")
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
            
            # 验证阶段
            self.model.eval()
            val_loss = 0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X = batch_X.to(self.device)
                    batch_y = batch_y.to(self.device)
                    
                    outputs = self.model(batch_X)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
            
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), self.model_save_path)
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch:3d}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch}')
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load(self.model_save_path))
        print("训练完成！")
    
    class DirectionWeightedLoss(nn.Module):
        """方向加权损失函数 - 专门优化方向准确性"""
        
        def __init__(self, mse_weight=0.3, direction_weight=0.7):
            super().__init__()
            self.mse_weight = mse_weight
            self.direction_weight = direction_weight
            self.mse_loss = nn.MSELoss()
        
        def forward(self, pred, target):
            # 1. 传统MSE损失
            mse_loss = self.mse_loss(pred, target)
            
            # 2. 方向损失
            pred_sign = torch.sign(pred)
            target_sign = torch.sign(target)
            
            # 方向一致性损失（方向不一致时惩罚更大）
            direction_loss = torch.mean((pred_sign - target_sign) ** 2)
            
            # 3. 组合损失
            total_loss = self.mse_weight * mse_loss + self.direction_weight * direction_loss
            
            return total_loss
    
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                outputs = self.model(batch_X)
                predictions.append(outputs.cpu().numpy())
                actuals.append(batch_y.cpu().numpy())
        
        predictions = np.vstack(predictions)
        actuals = np.vstack(actuals)
        
        return predictions, actuals


class UnifiedModelTrainer:
    """统一的模型训练器，支持GRU、Transformer和XGBoost"""
    
    def __init__(self, model_type='gru', **model_params):
        self.model_type = model_type.lower()
        self.model_params = model_params
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
    def create_model(self, input_size, num_targets):
        """创建指定类型的模型"""
        # 🔧 重要修改：只输出1个目标（30分钟收益率），不是6个目标
        actual_targets = 1  # 只预测30分钟收益率
        
        if self.model_type == 'gru':
            self.model = SimpleGRU(
                input_size=input_size,
                hidden_size=self.model_params.get('hidden_size', 64),
                num_layers=self.model_params.get('num_layers', 1),
                dropout=self.model_params.get('dropout', 0.1),
                num_targets=actual_targets  # 只输出1个值
            ).to(self.device)
            
        elif self.model_type == 'transformer':
            self.model = SimpleTransformer(
                input_size=input_size,
                d_model=self.model_params.get('d_model', 64),
                nhead=self.model_params.get('nhead', 8),
                num_layers=self.model_params.get('num_layers', 2),
                dropout=self.model_params.get('dropout', 0.1),
                num_targets=actual_targets  # 只输出1个值
            ).to(self.device)
            
        elif self.model_type == 'xgboost':
            if not XGBOOST_AVAILABLE:
                raise ImportError("XGBoost not available")
            self.model = XGBoostModel(
                num_targets=actual_targets,  # 只输出1个值
                **self.model_params
            )
            
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
        
        print(f"✅ 创建了 {self.model_type.upper()} 模型 (只预测30分钟收益率)")
        return self.model
    
    def train(self, X_train, y_train, X_val, y_val, epochs=30, lr=0.001, patience=5):
        """训练模型"""
        if self.model_type == 'xgboost':
            # XGBoost训练
            print("🚀 开始训练XGBoost模型...")
            if self.model is None:
                raise ValueError("XGBoost模型未初始化，请先调用create_model()")
            self.model.fit(X_train, y_train, X_val, y_val, verbose=True)
            
        else:
            # PyTorch模型训练
            print(f"🚀 开始训练{self.model_type.upper()}模型...")
            
            if self.model is None:
                raise ValueError(f"{self.model_type.upper()}模型未初始化，请先调用create_model()")
            
            # 创建数据加载器
            batch_size = 32
            train_loader = DataLoader(
                TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train)),
                batch_size=batch_size, shuffle=True
            )
            val_loader = DataLoader(
                TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val)),
                batch_size=batch_size, shuffle=False
            )
            
            # 使用MLTrainer训练（启用方向加权损失）
            trainer = MLTrainer(self.model, self.device)
            trainer.model_save_path = f'best_{self.model_type}_model.pth'
            trainer.train_model(train_loader, val_loader, epochs=epochs, lr=lr, patience=patience, use_direction_loss=True)
    
    def predict(self, X_test, y_test=None):
        """预测"""
        if self.model_type == 'xgboost':
            # XGBoost预测
            if self.model is None:
                raise ValueError("XGBoost模型未初始化，请先调用create_model()并训练")
            predictions = self.model.predict(X_test)
            if y_test is not None:
                return predictions, y_test
            else:
                return predictions
            
        else:
            # PyTorch模型预测
            if self.model is None:
                raise ValueError(f"{self.model_type.upper()}模型未初始化，请先调用create_model()并训练")
            
            test_loader = DataLoader(
                TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test)),
                batch_size=32, shuffle=False
            )
            
            trainer = MLTrainer(self.model, self.device)
            return trainer.predict(test_loader)


class OptimizedMLPipeline:
    """优化的机器学习管道"""
    
    def __init__(self):
        self.factor_calculator = EnhancedFactorCalculator()
        self.scaler = None
        
    def create_labels(self, df):
        """创建预测标签"""
        print("📊 创建预测标签...")
        
        labels = {}
        
        # 多周期收益率预测
        # periods = [10, 30, 60]  # 10分钟、30分钟、1小时
        periods = [60]
        
        for period in periods:
            # 未来收益率
            future_return = df['close'].shift(-period) / df['close'] - 1
            labels[f'return_{period}min'] = future_return   # 转换为百分比
            
            # 方向分类
            direction_labels = pd.cut(
                future_return,
                bins=[-np.inf, -0.005, 0.005, np.inf],
                labels=[0, 1, 2]
            )
            labels[f'direction_{period}min'] = direction_labels.astype(float)
        
        return pd.DataFrame(labels, index=df.index)
    
    def clean_data(self, factors_df, labels_df):
        """数据清理"""
        print("🧹 数据清理...")
        
        # 合并数据
        combined_df = pd.concat([factors_df, labels_df], axis=1)
        
        # 🔧 修复：处理无穷大值和异常值
        print("  处理无穷大值和异常值...")
        for col in combined_df.columns:
            if combined_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                # 替换无穷大值为NaN
                combined_df[col] = combined_df[col].replace([np.inf, -np.inf], np.nan)
                
                # 处理异常值（使用IQR方法）
                Q1 = combined_df[col].quantile(0.25)
                Q3 = combined_df[col].quantile(0.75)
                IQR = Q3 - Q1
                
                # 定义异常值边界
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR
                
                # 将异常值替换为边界值
                combined_df[col] = combined_df[col].clip(lower_bound, upper_bound)
        
        # 删除缺失值过多的列
        threshold = len(combined_df) * 0.5
        na_counts = combined_df.isnull().sum()
        good_columns = na_counts[na_counts < threshold].index
        
        combined_df = combined_df[good_columns]
        
        # 智能填充
        print("  智能填充缺失值...")
        for col in combined_df.columns:
            if combined_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                # 前向填充
                combined_df.loc[:, col] = combined_df[col].fillna(method='ffill')
                # 后向填充
                combined_df.loc[:, col] = combined_df[col].fillna(method='bfill')
                # 最后用中位数填充
                combined_df.loc[:, col] = combined_df[col].fillna(combined_df[col].median())
        
        # 删除剩余的NaN行
        combined_df = combined_df.dropna()
        
        # 最终检查：确保没有无穷大值
        print("  最终数据质量检查...")
        for col in combined_df.columns:
            if combined_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                inf_count = np.isinf(combined_df[col]).sum()
                if inf_count > 0:
                    print(f"    警告: {col} 仍有 {inf_count} 个无穷大值，用中位数替换")
                    combined_df.loc[:, col] = combined_df[col].replace([np.inf, -np.inf], combined_df[col].median())
        
        print(f"  清理后数据: {len(combined_df)} 行, {len(combined_df.columns)} 列")
        
        return combined_df
    
    def create_sequences(self, features, labels, sequence_length=30):
        """创建序列数据"""
        print("📦 创建序列数据...")
        
        X, y = [], []
        timestamps = []
        
        for i in range(sequence_length, len(features)):
            # 特征序列
            feature_seq = features.iloc[i-sequence_length:i].values
            # 标签
            label_vals = labels.iloc[i].values
            
            # 检查数据完整性
            if not (np.isnan(feature_seq).any() or np.isnan(label_vals).any()):
                X.append(feature_seq)
                y.append(label_vals)
                timestamps.append(features.index[i])
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.float32)
        
        print(f"  序列数据: X={X.shape}, y={y.shape}")
        
        return X, y, timestamps
    
    def split_data(self, X, y, timestamps, train_ratio=0.7, val_ratio=0.15):
        """数据分割 - 时间序列分割，不打乱顺序"""
        n = len(X)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        # 按时间顺序分割，不打乱
        return (
            (X[:train_end], y[:train_end], timestamps[:train_end]),
            (X[train_end:val_end], y[train_end:val_end], timestamps[train_end:val_end]),
            (X[val_end:], y[val_end:], timestamps[val_end:])
        )
    
    def normalize_features(self, X_train, X_val, X_test):
        """特征标准化"""
        print("🔄 特征标准化...")
        
        # 重塑数据
        n_samples, seq_len, n_features = X_train.shape
        X_train_reshaped = X_train.reshape(-1, n_features)
        X_val_reshaped = X_val.reshape(-1, n_features)
        X_test_reshaped = X_test.reshape(-1, n_features)
        
        # 使用RobustScaler
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train_reshaped)
        X_val_scaled = self.scaler.transform(X_val_reshaped)
        X_test_scaled = self.scaler.transform(X_test_reshaped)
        
        # 重塑回原形状
        X_train_scaled = X_train_scaled.reshape(X_train.shape)
        X_val_scaled = X_val_scaled.reshape(X_val.shape)
        X_test_scaled = X_test_scaled.reshape(X_test.shape)
        
        return X_train_scaled, X_val_scaled, X_test_scaled
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=50, lr=0.001):
        """训练模型"""
        print("🚀 开始训练GRU模型...")
        
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"  使用设备: {device}")
        
        # 创建模型
        input_size = X_train.shape[2]
        num_targets = y_train.shape[1]
        
        model = SimpleGRU(
            input_size=input_size,
            hidden_size=256,
            num_layers=2,
            dropout=0.1,
            num_targets=num_targets
        ).to(device)
        
        # 优化器和损失函数
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # 创建数据加载器
        batch_size = 64
        train_loader = DataLoader(
            TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train)),
            batch_size=batch_size, shuffle=True
        )
        val_loader = DataLoader(
            TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val)),
            batch_size=batch_size
        )
        
        # 训练循环
        best_val_loss = float('inf')
        patience_counter = 0
        train_losses = []
        val_losses = []
        
        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            train_count = 0
            
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(device)
                batch_y = batch_y.to(device)
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
                train_count += 1
            
            # 验证阶段
            model.eval()
            val_loss = 0
            val_count = 0
            
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X = batch_X.to(device)
                    batch_y = batch_y.to(device)
                    
                    outputs = model(batch_X)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
                    val_count += 1
            
            avg_train_loss = train_loss / train_count
            avg_val_loss = val_loss / val_count
            
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            
            scheduler.step(avg_val_loss)
            
            # 早停
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                torch.save(model.state_dict(), 'best_gru_model.pth')
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'  Epoch {epoch:3d}: Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
            
            if patience_counter >= 10:
                print(f'  早停于epoch {epoch}')
                break
        
        # 加载最佳模型
        model.load_state_dict(torch.load('best_gru_model.pth'))
        
        print("✅ GRU模型训练完成！")
        
        return model, train_losses, val_losses
    
    def evaluate_model(self, model, X_test, y_test, target_names):
        """评估模型"""
        print("📊 模型评估...")
        
        device = next(model.parameters()).device
        
        # 预测
        model.eval()
        test_loader = DataLoader(
            TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test)),
            batch_size=64
        )
        
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X = batch_X.to(device)
                batch_y = batch_y.to(device)
                
                outputs = model(batch_X)
                predictions.append(outputs.cpu().numpy())
                actuals.append(batch_y.cpu().numpy())
        
        predictions = np.vstack(predictions)
        actuals = np.vstack(actuals)
        
        # 评估指标
        print("\n📈 模型性能报告:")
        print("=" * 60)
        
        for i, target in enumerate(target_names):
            pred = predictions[:, i]
            actual = actuals[:, i]
            
            mse = mean_squared_error(actual, pred)
            mae = mean_absolute_error(actual, pred)
            corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
            
            # 方向准确性
            pred_direction = np.sign(pred)
            actual_direction = np.sign(actual)
            direction_accuracy = np.mean(pred_direction == actual_direction)
            
            print(f"\n{target}:")
            print(f"  MSE: {mse:.6f}")
            print(f"  MAE: {mae:.6f}")
            print(f"  相关性: {corr:.4f}")
            print(f"  方向准确性: {direction_accuracy:.2%}")
        
        return predictions, actuals
    
    def run_complete_pipeline(self, df):
        """运行完整管道"""
        print("🚀 启动完整的机器学习管道")
        print("=" * 70)
        
        # 1. 计算因子
        print("\n第一步: 计算因子")
        factors_df = self.factor_calculator.calculate_all_factors(df)
        
        # 2. 创建标签
        print("\n第二步: 创建标签")
        labels_df = self.create_labels(df)
        
        # 3. 数据清理
        print("\n第三步: 数据清理")
        combined_df = self.clean_data(factors_df, labels_df)
        
        if len(combined_df) < 1000:
            print("❌ 数据量不足")
            return None
        
        # 分离特征和标签
        feature_columns = [col for col in combined_df.columns if col in factors_df.columns]
        label_columns = [col for col in combined_df.columns if col in labels_df.columns]
        
        features = combined_df[feature_columns]
        labels = combined_df[label_columns]
        
        # 🔧 重要：只用30分钟收益率作为训练目标
        training_label = labels[['return_30min']]  # 只取30分钟收益率
        evaluation_labels = labels[['return_10min', 'return_60min', 'return_120min']]  # 用于评估的标签
        
        print(f"\n🎯 标签使用情况:")
        print(f"  训练标签: {list(training_label.columns)}")
        print(f"  评估标签: {list(evaluation_labels.columns)}")
        
        # 4. 创建序列
        print("\n第四步: 创建序列")
        sequence_length = 30  # 定义序列长度
        target_periods = [10, 30, 60, 120]  # 定义目标周期
        
        X, y_train, y_eval = [], [], []
        timestamps = []
        
        for i in range(sequence_length, len(features) - max(target_periods)):
            feature_seq = features.iloc[i-sequence_length:i].values
            train_label = training_label.iloc[i].values  # 只有30分钟收益率
            eval_labels = evaluation_labels.iloc[i].values  # 10, 60, 120分钟收益率
            
            # 简单的 NaN 检查
            if (not pd.isna(feature_seq).any() and 
                not pd.isna(train_label).any() and 
                not pd.isna(eval_labels).any()):
                X.append(feature_seq)
                y_train.append(train_label)
                y_eval.append(eval_labels)
                timestamps.append(features.index[i])
        
        X = np.array(X, dtype=np.float32)
        y_train = np.array(y_train, dtype=np.float32)  # 只有30分钟收益率
        y_eval = np.array(y_eval, dtype=np.float32)    # 10, 60, 120分钟收益率
        
        print(f"序列数据:")
        print(f"  X (特征): {X.shape}")
        print(f"  y_train (训练标签): {y_train.shape}")
        print(f"  y_eval (评估标签): {y_eval.shape}")
        
        if len(X) < 200:
            print("❌ 错误: 有效序列数据过少")
            return None
        
        # 5. 数据分割
        print("\n第五步: 数据分割")
        n = len(X)
        train_end = int(n * 0.7)
        val_end = int(n * 0.85)
        
        # 按时间顺序分割训练数据
        X_train, y_train_split = X[:train_end], y_train[:train_end]
        X_val, y_val_split = X[train_end:val_end], y_train[train_end:val_end]
        X_test, y_test_split = X[val_end:], y_train[val_end:]
        
        # 同时分割评估数据
        y_eval_train = y_eval[:train_end]
        y_eval_val = y_eval[train_end:val_end]
        y_eval_test = y_eval[val_end:]
        
        print(f"训练集时间范围: {timestamps[0]} 到 {timestamps[train_end-1]}")
        print(f"验证集时间范围: {timestamps[train_end]} 到 {timestamps[val_end-1]}")
        print(f"测试集时间范围: {timestamps[val_end]} 到 {timestamps[-1]}")
        
        print(f"\n数据分割结果:")
        print(f"  训练集: X{X_train.shape}, y_train{y_train_split.shape}, y_eval{y_eval_train.shape}")
        print(f"  验证集: X{X_val.shape}, y_val{y_val_split.shape}, y_eval{y_eval_val.shape}")
        print(f"  测试集: X{X_test.shape}, y_test{y_test_split.shape}, y_eval{y_eval_test.shape}")
        
        # 6. 特征标准化
        print("\n第六步: 特征标准化")
        n_features = X_train.shape[2]
        X_train_flat = X_train.reshape(-1, n_features)
        
        # 计算训练集统计量
        train_mean = np.mean(X_train_flat, axis=0)
        train_std = np.std(X_train_flat, axis=0) + 1e-8
        
        # 🔧 修复：检查标准化参数
        print(f"  标准化参数检查:")
        print(f"    均值范围: [{train_mean.min():.6f}, {train_mean.max():.6f}]")
        print(f"    标准差范围: [{train_std.min():.6f}, {train_std.max():.6f}]")
        
        # 检查是否有异常值
        if np.any(np.isnan(train_mean)) or np.any(np.isnan(train_std)):
            print("❌ 错误: 标准化参数包含NaN")
            return None
        
        if np.any(train_std < 1e-6):
            print("⚠️ 警告: 某些特征标准差过小，使用RobustScaler")
            # 使用RobustScaler作为备选
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()
            X_train_scaled = scaler.fit_transform(X_train_flat).reshape(X_train.shape)
            X_val_scaled = scaler.transform(X_val.reshape(-1, n_features)).reshape(X_val.shape)
            X_test_scaled = scaler.transform(X_test.reshape(-1, n_features)).reshape(X_test.shape)
        else:
            # 标准化
            X_train_scaled = ((X_train.reshape(-1, n_features) - train_mean) / train_std).reshape(X_train.shape)
            X_val_scaled = ((X_val.reshape(-1, n_features) - train_mean) / train_std).reshape(X_val.shape)
            X_test_scaled = ((X_test.reshape(-1, n_features) - train_mean) / train_std).reshape(X_test.shape)
        
        # 最终检查标准化后的数据
        print(f"  标准化后数据检查:")
        print(f"    训练集范围: [{X_train_scaled.min():.6f}, {X_train_scaled.max():.6f}]")
        print(f"    验证集范围: [{X_val_scaled.min():.6f}, {X_val_scaled.max():.6f}]")
        print(f"    测试集范围: [{X_test_scaled.min():.6f}, {X_test_scaled.max():.6f}]")
        
        # 检查是否有NaN或无穷大
        if (np.any(np.isnan(X_train_scaled)) or np.any(np.isinf(X_train_scaled)) or
            np.any(np.isnan(X_val_scaled)) or np.any(np.isinf(X_val_scaled)) or
            np.any(np.isnan(X_test_scaled)) or np.any(np.isinf(X_test_scaled))):
            print("❌ 错误: 标准化后数据包含NaN或无穷大")
            return None
        
        print(f"训练集: {X_train_scaled.shape}")
        print(f"验证集: {X_val_scaled.shape}")
        print(f"测试集: {X_test_scaled.shape}")
        
        # 7. 模型训练
        print("\n第七步: 模型训练")
        
        input_size = X_train_scaled.shape[2]
        num_targets = y_train_split.shape[1]  # 应该是1（只有30分钟收益率）
        
        # 创建统一的模型训练器
        trainer = UnifiedModelTrainer(model_type=model_type, **model_params)
        model = trainer.create_model(input_size, num_targets)
        
        # 训练模型
        trainer.train(X_train_scaled, y_train_split, X_val_scaled, y_val_split, epochs=30, patience=5)
        
        # 8. 预测和评估
        print("\n第八步: 模型评估")
        predictions, actuals = trainer.predict(X_test_scaled, y_test_split)
        
        # 🔧 关键：用30分钟的预测结果来评估10分钟、60分钟、120分钟的效果
        print("\n📊 模型性能报告:")
        print("="*50)
        print(f"模型训练目标: 30分钟收益率预测")
        print(f"模型输出维度: {predictions.shape}")
        
        # 评估30分钟预测效果
        pred_30min = predictions[:, 0]  # 30分钟预测
        actual_30min = actuals[:, 0]    # 30分钟实际
        
        mse_30min = np.mean((pred_30min - actual_30min) ** 2)
        corr_30min = np.corrcoef(pred_30min, actual_30min)[0, 1] if len(pred_30min) > 1 else 0
        direction_acc_30min = np.mean(np.sign(pred_30min) == np.sign(actual_30min))
        
        print(f"\n🎯 30分钟收益率预测 (训练目标):")
        print(f"  MSE: {mse_30min:.6f}")
        print(f"  相关性: {corr_30min:.4f}")
        print(f"  方向准确性: {direction_acc_30min:.2%}")
        
        # 用30分钟的预测来评估其他时间段
        evaluation_names = ['10分钟', '60分钟', '120分钟']
        print(f"\n📈 用30分钟预测评估其他时间段:")
        
        for i, eval_name in enumerate(evaluation_names):
            actual_eval = y_eval_test[:, i]  # 实际的10/60/120分钟收益率
            
            # 用30分钟的预测来评估
            mse_eval = np.mean((pred_30min - actual_eval) ** 2)
            corr_eval = np.corrcoef(pred_30min, actual_eval)[0, 1] if len(pred_30min) > 1 else 0
            direction_acc_eval = np.mean(np.sign(pred_30min) == np.sign(actual_eval))
            
            print(f"  {eval_name}收益率:")
            print(f"    MSE: {mse_eval:.6f}")
            print(f"    相关性: {corr_eval:.4f}")
            print(f"    方向准确性: {direction_acc_eval:.2%}")
        
        print("✅ 训练流程完成！")
        
        return {
            'model': model,
            'predictions': predictions,
            'actuals': actuals,
            'eval_predictions': pred_30min,
            'eval_targets': y_eval_test,
            'feature_columns': features.columns.tolist(),
            'target_names': ['return_30min'],
            'evaluation_names': evaluation_names,
            'data_stats': {
                'original_rows': len(df),
                'final_rows': len(combined_df),
                'sequence_count': len(X),
                'feature_count': len(features.columns)
            }
        }


def main_training_pipeline(df, model_type='gru', **model_params):
    """优化的主训练和评估流程 - 支持多种模型"""
    
    print(f"🚀 启动优化的机器学习训练流程 - 使用 {model_type.upper()} 模型")
    print("="*60)
    
    # 1. 数据质量诊断和清洗
    print("第一步: 数据质量管理")
    
    # 🎯 使用新的高质量因子计算器
    hq_factor_calculator = HighQualityPriceVolumeFactor()
    
    # 诊断原始数据
    print("📊 原始数据统计:")
    print(f"  数据范围: {df.index[0]} 到 {df.index[-1]}")
    print(f"  数据量: {len(df)} 行")
    print(f"  列: {list(df.columns)}")
    
    # 计算高质量因子
    clean_factors = hq_factor_calculator.calculate_all_factors(df)
    
    # 最终质量检查
    print(f"\n✅ 最终数据集质量:")
    print(f"  行数: {len(clean_factors):,}")
    print(f"  列数: {len(clean_factors.columns)}")
    print(f"  总 NaN 数量: {clean_factors.isnull().sum().sum()}")
    print(f"  数据完整性: {(1 - clean_factors.isnull().sum().sum() / (len(clean_factors) * len(clean_factors.columns))):.1%}")
    
    # 继续原来的流程...
    if len(clean_factors) < 1000:
        print("❌ 错误: 清洗后数据量过少，无法进行有效训练")
        print(f"   当前数据量: {len(clean_factors)} 行")
        print("   建议: 检查原始数据质量或增加数据量")
        return None
    
    # 2. 创建简化但稳健的标签
    print("\n第二步: 创建预测标签")
    labels = {}
    
    # 创建多个时间段的收益率标签，但只用30分钟做训练
    target_periods = [10, 30, 60, 120]  # 10分钟、30分钟、1小时、2小时
    
    # 🔧 新增：统计整个数据集的收益率分布
    print("\n📊 整个数据集收益率分布统计:")
    print("="*50)
    for period in target_periods:
        future_return = df['close'].shift(-period) / df['close'] - 1
        future_return = future_return.dropna()
        pos_count = np.sum(future_return >= 0)
        neg_count = np.sum(future_return < 0)
        total = len(future_return)
        print(f"return_{period}min (收益率):")
        print(f"  上涨 (>=0): {pos_count} ({pos_count/total:.1%})")
        print(f"  下跌 (<0): {neg_count} ({neg_count/total:.1%})")
        print(f"  样本时间范围: {future_return.index[0]} 到 {future_return.index[-1]}")
        print()
    
    # 只创建回归标签（收益率），不创建分类标签
    for period in target_periods:
        future_return = df['close'].shift(-period) / df['close'] - 1
        labels[f'return_{period}min'] = future_return * 100  # 转换为百分比
    
                # 🔧 重要：只用30分钟作为训练目标
    training_target = 'return_30min'
    evaluation_targets = ['return_10min', 'return_60min', 'return_120min']
    
    print(f"\n🎯 训练设置:")
    print(f"  训练目标: {training_target}")
    print(f"  评估目标: {evaluation_targets}")
    
    labels_df = pd.DataFrame(labels, index=df.index)
    
    # 与因子数据对齐
    common_index = clean_factors.index.intersection(labels_df.index)
    clean_factors_aligned = clean_factors.loc[common_index]
    labels_df_aligned = labels_df.loc[common_index]
    
    # 🎯 因子重要性分析与选择
    print("\n第二步: 因子重要性分析与选择")
    target_series = labels_df_aligned[training_target]
    
    # 选择最佳因子（放宽条件）
    best_factors, selected_factor_stats = hq_factor_calculator.select_best_factors(
        clean_factors_aligned, target_series, top_n=35, min_correlation=0.005
    )
    
    # 用选择的因子替换原始因子
    clean_factors_aligned = best_factors
    
    # 最终清理
    combined_df = pd.concat([clean_factors_aligned, labels_df_aligned], axis=1)
    combined_df = combined_df.dropna()
    
    print(f"\n最终数据集: {len(combined_df)} 行 × {len(combined_df.columns)} 列")
    
    if len(combined_df) < 500:
        print("❌ 错误: 最终数据集过小")
        return None
        
            # 3. 创建序列数据（简化版）
    print("\n第三步: 创建训练序列")
    sequence_length = 60  # 1小时历史数据
    target_periods = [10, 30, 60, 120]  # 定义目标周期
    
    features = combined_df[clean_factors_aligned.columns]
    labels_clean = combined_df[labels_df_aligned.columns]
    
    X, y = [], []
    timestamps = []
    
    for i in range(sequence_length, len(features) - max(target_periods)):
        feature_seq = features.iloc[i-sequence_length:i].values
        label_vals = labels_clean.iloc[i].values
        
        # 简单的 NaN 检查
        if not pd.isna(feature_seq).any() and not pd.isna(label_vals).any():
            X.append(feature_seq)
            y.append(label_vals)
            timestamps.append(features.index[i])
    
    X = np.array(X, dtype=np.float32)
    y = np.array(y, dtype=np.float32)
    
    print(f"序列数据: X={X.shape}, y={y.shape}")
    
    if len(X) < 200:
        print("❌ 错误: 有效序列数据过少")
        return None
    
    # 4. 时间序列分割 - 按时间顺序分割
    print("\n第四步: 数据分割")
    n = len(X)
    train_end = int(n * 0.7)
    val_end = int(n * 0.85)
    
    # 按时间顺序分割，不打乱
    X_train, y_train = X[:train_end], y[:train_end]
    X_val, y_val = X[train_end:val_end], y[train_end:val_end]
    X_test, y_test = X[val_end:], y[val_end:]
    
    print(f"训练集时间范围: {timestamps[0]} 到 {timestamps[train_end-1]}")
    print(f"验证集时间范围: {timestamps[train_end]} 到 {timestamps[val_end-1]}")
    print(f"测试集时间范围: {timestamps[val_end]} 到 {timestamps[-1]}")
    
    # 5. 特征标准化
    print("\n第五步: 特征标准化")
    n_features = X_train.shape[2]
    X_train_flat = X_train.reshape(-1, n_features)
    
    # 计算训练集统计量
    train_mean = np.mean(X_train_flat, axis=0)
    train_std = np.std(X_train_flat, axis=0) + 1e-8
    
    # 🔧 修复：检查标准化参数
    print(f"  标准化参数检查:")
    print(f"    均值范围: [{train_mean.min():.6f}, {train_mean.max():.6f}]")
    print(f"    标准差范围: [{train_std.min():.6f}, {train_std.max():.6f}]")
    
    # 检查是否有异常值
    if np.any(np.isnan(train_mean)) or np.any(np.isnan(train_std)):
        print("❌ 错误: 标准化参数包含NaN")
        return None
    
    if np.any(train_std < 1e-6):
        print("⚠️ 警告: 某些特征标准差过小，使用RobustScaler")
        # 使用RobustScaler作为备选
        from sklearn.preprocessing import RobustScaler
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train_flat).reshape(X_train.shape)
        X_val_scaled = scaler.transform(X_val.reshape(-1, n_features)).reshape(X_val.shape)
        X_test_scaled = scaler.transform(X_test.reshape(-1, n_features)).reshape(X_test.shape)
    else:
        # 标准化
        X_train_scaled = ((X_train.reshape(-1, n_features) - train_mean) / train_std).reshape(X_train.shape)
        X_val_scaled = ((X_val.reshape(-1, n_features) - train_mean) / train_std).reshape(X_val.shape)
        X_test_scaled = ((X_test.reshape(-1, n_features) - train_mean) / train_std).reshape(X_test.shape)
    
    # 最终检查标准化后的数据
    print(f"  标准化后数据检查:")
    print(f"    训练集范围: [{X_train_scaled.min():.6f}, {X_train_scaled.max():.6f}]")
    print(f"    验证集范围: [{X_val_scaled.min():.6f}, {X_val_scaled.max():.6f}]")
    print(f"    测试集范围: [{X_test_scaled.min():.6f}, {X_test_scaled.max():.6f}]")
    
    # 检查是否有NaN或无穷大
    if (np.any(np.isnan(X_train_scaled)) or np.any(np.isinf(X_train_scaled)) or
        np.any(np.isnan(X_val_scaled)) or np.any(np.isinf(X_val_scaled)) or
        np.any(np.isnan(X_test_scaled)) or np.any(np.isinf(X_test_scaled))):
        print("❌ 错误: 标准化后数据包含NaN或无穷大")
        return None
    
    print(f"训练集: {X_train_scaled.shape}")
    print(f"验证集: {X_val_scaled.shape}")
    print(f"测试集: {X_test_scaled.shape}")
    
    # 6. 模型训练 (针对高质量因子优化)
    print("\n第六步: 模型训练")
    
    input_size = X_train_scaled.shape[2]
    num_targets = y_train.shape[1]
    
    # 🎯 针对高质量因子系统调整训练参数
    print(f"🎯 针对高质量因子系统优化训练参数:")
    print(f"  输入特征数: {input_size}")
    print(f"  输出目标数: {num_targets}")
    print(f"  训练样本数: {len(X_train_scaled)}")
    
    # 创建统一的模型训练器
    trainer = UnifiedModelTrainer(model_type=model_type, **model_params)
    model = trainer.create_model(input_size, num_targets)
    
    # 🎯 针对准确性优化训练参数
    optimized_epochs = 200  # 大幅增加训练轮数
    optimized_patience = 30  # 增加耐心值，避免过早停止
    optimized_lr = 0.0005  # 降低学习率，提高学习稳定性
    
    # 根据模型类型调整参数
    if model_type == 'gru':
        # GRU模型参数优化
        if 'hidden_size' not in model_params:
            model_params['hidden_size'] = 128  # 增加隐藏层大小
        if 'num_layers' not in model_params:
            model_params['num_layers'] = 2  # 增加层数
        if 'dropout' not in model_params:
            model_params['dropout'] = 0.2  # 增加dropout防止过拟合
    elif model_type == 'transformer':
        # Transformer模型参数优化
        if 'd_model' not in model_params:
            model_params['d_model'] = 128
        if 'num_layers' not in model_params:
            model_params['num_layers'] = 3
        if 'dropout' not in model_params:
            model_params['dropout'] = 0.15
    
    print(f"  🎯 准确性优化训练参数:")
    print(f"    训练轮数: {optimized_epochs}")
    print(f"    早停耐心: {optimized_patience}")
    print(f"    学习率: {optimized_lr}")
    print(f"    模型参数: {model_params}")
    
    # 重新创建优化后的模型
    trainer = UnifiedModelTrainer(model_type=model_type, **model_params)
    model = trainer.create_model(input_size, num_targets)
    
    # 训练模型
    trainer.train(X_train_scaled, y_train, X_val_scaled, y_val, 
                  epochs=optimized_epochs, patience=optimized_patience, lr=optimized_lr)
    
    # 7. 预测和评估
    print("\n第七步: 模型评估")
    predictions, actuals = trainer.predict(X_test_scaled, y_test)
    
    # 🔧 修复：只评估实际的输出目标（30分钟收益率）
    target_names = ['return_30min']  # 只有一个目标
    
    print("\n📊 模型性能报告:")
    print("="*50)
    print(f"模型预测维度: {predictions.shape}")
    print(f"实际标签维度: {actuals.shape}")
    
    for i, target in enumerate(target_names):
        if i < predictions.shape[1]:  # 确保索引不越界
            pred_vals = predictions[:, i]
            actual_vals = actuals[:, i]
        else:
            print(f"跳过 {target}：索引超出范围")
            continue
        
        # 基础指标
        mse = np.mean((pred_vals - actual_vals) ** 2)
        corr = np.corrcoef(pred_vals, actual_vals)[0, 1] if len(pred_vals) > 1 else 0
        
        print(f"\n🎯 {target} 详细性能分析:")
        print(f"  MSE: {mse:.6f}")
        print(f"  相关性: {corr:.4f}")
        
        # 方向准确性评估
        if 'return' in target:
            # 收益率预测的方向准确性
            pred_direction = np.sign(pred_vals)
            actual_direction = np.sign(actual_vals)
            direction_accuracy = np.mean(pred_direction == actual_direction)
            print(f"  方向准确性: {direction_accuracy:.2%}")
            
            # 🎯 详细的准确性分析
            print(f"\n  📊 详细准确性分析:")
            
            # 分阈值统计
            thresholds = [0.001, 0.005, 0.01, 0.02]
            for threshold in thresholds:
                # 预测的强信号
                strong_up_pred = pred_vals > threshold
                strong_down_pred = pred_vals < -threshold
                
                # 实际的强信号
                strong_up_actual = actual_vals > threshold
                strong_down_actual = actual_vals < -threshold
                
                # 强信号准确性
                if np.sum(strong_up_pred) > 0:
                    strong_up_acc = np.mean(actual_vals[strong_up_pred] > 0)
                    print(f"    强上涨预测准确性 (>{threshold:.1%}): {strong_up_acc:.1%} (样本数: {np.sum(strong_up_pred)})")
                
                if np.sum(strong_down_pred) > 0:
                    strong_down_acc = np.mean(actual_vals[strong_down_pred] < 0)
                    print(f"    强下跌预测准确性 (<{-threshold:.1%}): {strong_down_acc:.1%} (样本数: {np.sum(strong_down_pred)})")
            
            # 分位数分析
            print(f"\n  📈 分位数分析:")
            pred_quantiles = np.quantile(pred_vals, [0.1, 0.3, 0.7, 0.9])
            
            # 最高10%的预测
            top_10_mask = pred_vals >= pred_quantiles[3]
            if np.sum(top_10_mask) > 0:
                top_10_acc = np.mean(actual_vals[top_10_mask] > 0)
                print(f"    最高10%预测的准确性: {top_10_acc:.1%}")
            
            # 最低10%的预测
            bottom_10_mask = pred_vals <= pred_quantiles[0]
            if np.sum(bottom_10_mask) > 0:
                bottom_10_acc = np.mean(actual_vals[bottom_10_mask] < 0)
                print(f"    最低10%预测的准确性: {bottom_10_acc:.1%}")
            
            # 中性区间
            neutral_mask = (pred_vals > pred_quantiles[1]) & (pred_vals < pred_quantiles[2])
            if np.sum(neutral_mask) > 0:
                neutral_acc = np.mean(np.abs(actual_vals[neutral_mask]) < 0.005)
                print(f"    中性区间预测准确性: {neutral_acc:.1%}")
        
        # 🎯 如果准确率超过55%，给出特别提示
        if direction_accuracy > 0.55:
            print(f"\n  🎉 恭喜! 方向准确性 {direction_accuracy:.2%} 已达到55%+目标!")
        elif direction_accuracy > 0.52:
            print(f"\n  🔥 接近目标! 方向准确性 {direction_accuracy:.2%} 接近55%目标")
        else:
            print(f"\n  ⚠️  准确性 {direction_accuracy:.2%} 需要进一步优化")
    
    print("\n✅ 高质量因子系统训练完成！")
    
    return {
        'model': model,
        'predictions': predictions,
        'actuals': actuals,
        'feature_columns': clean_factors_aligned.columns.tolist(),
        'target_names': target_names,
        'data_stats': {
            'original_rows': len(df),
            'final_rows': len(combined_df),
            'sequence_count': len(X),
            'feature_count': len(clean_factors_aligned.columns)
        }
    }


class HighQualityPriceVolumeFactor:
    """
    高质量量价因子计算器
    专注于经过验证的有效因子，避免过度拟合
    """
    
    def __init__(self):
        self.factor_groups = {
            'price_momentum': [],
            'volume_momentum': [],
            'price_volume_relation': [],
            'volatility': [],
            'trend': [],
            'reversal': []
        }
    
    def calculate_price_momentum_factors(self, df):
        """计算价格动量因子"""
        print("  计算价格动量因子...")
        factors = {}
        
        # 1. 简单价格动量 (经典因子)
        for period in [1, 3, 5, 10, 20]:
            factors[f'price_momentum_{period}'] = (df['close'] / df['close'].shift(period)) - 1
        
        # 2. 价格加速度
        factors['price_acceleration'] = df['close'].diff().diff()
        
        # 3. 价格位置 (相对高低点的位置)
        for period in [10, 20, 60]:
            high_roll = df['high'].rolling(period, min_periods=period//2).max()
            low_roll = df['low'].rolling(period, min_periods=period//2).min()
            factors[f'price_position_{period}'] = (df['close'] - low_roll) / (high_roll - low_roll + 1e-8)
        
        # 4. 相对强度 (vs 移动平均)
        for period in [5, 10, 20]:
            ma = df['close'].rolling(period, min_periods=period//2).mean()
            factors[f'relative_strength_{period}'] = (df['close'] / ma) - 1
        
        return factors
    
    def calculate_volume_momentum_factors(self, df):
        """计算成交量动量因子"""
        print("  计算成交量动量因子...")
        factors = {}
        
        if 'volume' not in df.columns:
            print("    警告: 无成交量数据，跳过成交量因子")
            return factors
        
        # 1. 成交量动量
        for period in [1, 3, 5, 10, 20]:
            factors[f'volume_momentum_{period}'] = (df['volume'] / df['volume'].shift(period)) - 1
        
        # 2. 成交量相对强度
        for period in [5, 10, 20]:
            vol_ma = df['volume'].rolling(period, min_periods=period//2).mean()
            factors[f'volume_relative_strength_{period}'] = (df['volume'] / vol_ma) - 1
        
        # 3. 成交量波动率
        for period in [5, 10, 20]:
            vol_returns = df['volume'].pct_change()
            factors[f'volume_volatility_{period}'] = vol_returns.rolling(period, min_periods=period//2).std()
        
        return factors
    
    def calculate_price_volume_relation_factors(self, df):
        """计算量价关系因子"""
        print("  计算量价关系因子...")
        factors = {}
        
        if 'volume' not in df.columns:
            print("    警告: 无成交量数据，跳过量价关系因子")
            return factors
        
        # 1. 量价相关性
        for period in [5, 10, 20]:
            price_change = df['close'].pct_change()
            volume_change = df['volume'].pct_change()
            factors[f'price_volume_corr_{period}'] = price_change.rolling(period, min_periods=period//2).corr(volume_change)
        
        # 2. 成交量加权平均价格 (VWAP)
        for period in [5, 10, 20]:
            vwap = (df['close'] * df['volume']).rolling(period, min_periods=period//2).sum() / df['volume'].rolling(period, min_periods=period//2).sum()
            factors[f'vwap_ratio_{period}'] = (df['close'] / vwap) - 1
        
        # 3. 价格*成交量
        factors['price_volume_product'] = df['close'] * df['volume']
        
        # 4. 成交量突破
        for period in [10, 20]:
            vol_ma = df['volume'].rolling(period, min_periods=period//2).mean()
            vol_std = df['volume'].rolling(period, min_periods=period//2).std()
            factors[f'volume_breakout_{period}'] = (df['volume'] - vol_ma) / (vol_std + 1e-8)
        
        return factors
    
    def calculate_volatility_factors(self, df):
        """计算波动率因子"""
        print("  计算波动率因子...")
        factors = {}
        
        # 1. 价格波动率
        returns = df['close'].pct_change()
        for period in [5, 10, 20, 60]:
            factors[f'price_volatility_{period}'] = returns.rolling(period, min_periods=period//2).std()
        
        # 2. 高低价波动率
        for period in [5, 10, 20]:
            hl_ratio = np.log(df['high'] / df['low'])
            factors[f'hl_volatility_{period}'] = hl_ratio.rolling(period, min_periods=period//2).std()
        
        # 3. 真实波动率 (ATR)
        tr = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift(1)),
                np.abs(df['low'] - df['close'].shift(1))
            )
        )
        for period in [5, 10, 20]:
            factors[f'atr_{period}'] = tr.rolling(period, min_periods=period//2).mean()
        
        # 4. 波动率比率
        factors['volatility_ratio_5_20'] = (returns.rolling(5, min_periods=3).std() / 
                                          returns.rolling(20, min_periods=10).std())
        
        return factors
    
    def calculate_trend_factors(self, df):
        """计算趋势因子"""
        print("  计算趋势因子...")
        factors = {}
        
        # 1. 移动平均趋势
        for period in [5, 10, 20]:
            ma = df['close'].rolling(period, min_periods=period//2).mean()
            factors[f'ma_trend_{period}'] = ma.diff()
        
        # 2. 价格趋势强度
        for period in [10, 20]:
            def trend_strength(x):
                if len(x) < 2:
                    return 0
                return np.corrcoef(x, np.arange(len(x)))[0, 1]
            
            factors[f'trend_strength_{period}'] = df['close'].rolling(period, min_periods=period//2).apply(trend_strength)
        
        # 3. 突破强度
        for period in [10, 20]:
            high_roll = df['high'].rolling(period, min_periods=period//2).max()
            low_roll = df['low'].rolling(period, min_periods=period//2).min()
            factors[f'breakout_strength_{period}'] = np.where(
                df['close'] > high_roll.shift(1), 
                (df['close'] - high_roll.shift(1)) / (high_roll.shift(1) + 1e-8),
                np.where(
                    df['close'] < low_roll.shift(1),
                    (df['close'] - low_roll.shift(1)) / (low_roll.shift(1) + 1e-8),
                    0
                )
            )
        
        return factors
    
    def calculate_reversal_factors(self, df):
        """计算反转因子"""
        print("  计算反转因子...")
        factors = {}
        
        # 1. RSI - 经典反转指标
        for period in [6, 14, 21]:
            factors[f'rsi_{period}'] = calculate_rsi(df['close'], period)
        
        # 2. 价格反转
        for period in [2, 3, 5]:
            factors[f'price_reversal_{period}'] = -df['close'].rolling(period, min_periods=period//2).sum()
        
        # 3. 超买超卖
        for period in [10, 20]:
            price_position = self._calculate_price_position(df, period)
            factors[f'overbought_{period}'] = (price_position > 0.8).astype(int)
            factors[f'oversold_{period}'] = (price_position < 0.2).astype(int)
        
        return factors
    
    def _calculate_price_position(self, df, period):
        """计算价格位置 (0-1之间)"""
        high_roll = df['high'].rolling(period, min_periods=period//2).max()
        low_roll = df['low'].rolling(period, min_periods=period//2).min()
        return (df['close'] - low_roll) / (high_roll - low_roll + 1e-8)
    
    def calculate_all_factors(self, df):
        """计算所有高质量因子"""
        print("🎯 计算高质量量价因子...")
        
        all_factors = {}
        
        # 1. 价格动量因子
        price_momentum = self.calculate_price_momentum_factors(df)
        all_factors.update(price_momentum)
        self.factor_groups['price_momentum'] = list(price_momentum.keys())
        
        # 2. 成交量动量因子
        volume_momentum = self.calculate_volume_momentum_factors(df)
        all_factors.update(volume_momentum)
        self.factor_groups['volume_momentum'] = list(volume_momentum.keys())
        
        # 3. 量价关系因子
        price_volume_relation = self.calculate_price_volume_relation_factors(df)
        all_factors.update(price_volume_relation)
        self.factor_groups['price_volume_relation'] = list(price_volume_relation.keys())
        
        # 4. 波动率因子
        volatility = self.calculate_volatility_factors(df)
        all_factors.update(volatility)
        self.factor_groups['volatility'] = list(volatility.keys())
        
        # 5. 趋势因子
        trend = self.calculate_trend_factors(df)
        all_factors.update(trend)
        self.factor_groups['trend'] = list(trend.keys())
        
        # 6. 反转因子
        reversal = self.calculate_reversal_factors(df)
        all_factors.update(reversal)
        self.factor_groups['reversal'] = list(reversal.keys())
        
        # 创建DataFrame
        factor_df = pd.DataFrame(all_factors, index=df.index)
        
        # 数据清理
        factor_df = self._clean_factors(factor_df)
        
        print(f"  ✅ 生成 {len(factor_df.columns)} 个高质量因子")
        print(f"  因子分组统计:")
        for group_name, factors in self.factor_groups.items():
            print(f"    {group_name}: {len(factors)} 个因子")
        
        return factor_df
    
    def _clean_factors(self, factor_df):
        """清理因子数据"""
        print("  清理因子数据...")
        
        # 替换无穷大值
        factor_df = factor_df.replace([np.inf, -np.inf], np.nan)
        
        # 删除全NaN的列
        factor_df = factor_df.dropna(axis=1, how='all')
        
        # 处理异常值 (使用3倍标准差)
        for col in factor_df.columns:
            if factor_df[col].dtype in ['float64', 'float32']:
                mean_val = factor_df[col].mean()
                std_val = factor_df[col].std()
                
                if std_val > 0:
                    # 3倍标准差范围
                    lower_bound = mean_val - 3 * std_val
                    upper_bound = mean_val + 3 * std_val
                    
                    # 限制异常值
                    factor_df[col] = factor_df[col].clip(lower_bound, upper_bound)
        
        return factor_df
    
    def get_factor_importance_stats(self, factor_df, target_series):
        """计算因子重要性统计"""
        print("📊 计算因子重要性...")
        
        importance_stats = {}
        
        for factor_name in factor_df.columns:
            factor_values = factor_df[factor_name].dropna()
            target_values = target_series.loc[factor_values.index]
            
            # 去除NaN值
            mask = ~(pd.isna(factor_values) | pd.isna(target_values))
            factor_clean = factor_values[mask]
            target_clean = target_values[mask]
            
            if len(factor_clean) > 10:
                # 相关性
                correlation = np.corrcoef(factor_clean, target_clean)[0, 1]
                
                # IC (信息系数)
                ic = correlation
                
                # 方向一致性
                factor_direction = np.sign(factor_clean.diff().dropna())
                target_direction = np.sign(target_clean.diff().dropna())
                
                # 对齐索引
                common_index = factor_direction.index.intersection(target_direction.index)
                if len(common_index) > 0:
                    direction_accuracy = (factor_direction.loc[common_index] == target_direction.loc[common_index]).mean()
                else:
                    direction_accuracy = 0
                
                importance_stats[factor_name] = {
                    'correlation': correlation,
                    'ic': ic,
                    'direction_accuracy': direction_accuracy,
                    'data_coverage': len(factor_clean) / len(factor_df),
                    'factor_group': self._get_factor_group(factor_name)
                }
        
        return importance_stats
    
    def _get_factor_group(self, factor_name):
        """获取因子所属的组别"""
        for group_name, factors in self.factor_groups.items():
            if factor_name in factors:
                return group_name
        return 'unknown'
    
    def select_best_factors(self, factor_df, target_series, top_n=30, min_correlation=0.02):
        """
        选择最有效的因子
        
        Args:
            factor_df: 因子数据框
            target_series: 目标序列
            top_n: 选择前N个因子
            min_correlation: 最小相关性阈值
        """
        print(f"🎯 因子选择: 从{len(factor_df.columns)}个因子中选择最佳因子...")
        
        # 计算因子重要性
        importance_stats = self.get_factor_importance_stats(factor_df, target_series)
        
        # 过滤有效因子
        valid_factors = []
        for factor_name, stats in importance_stats.items():
            # 过滤条件：
            # 1. 相关性不为NaN
            # 2. 相关性绝对值大于阈值
            # 3. 方向准确性大于48%（随机水平）
            if (not pd.isna(stats['correlation']) and 
                abs(stats['correlation']) >= min_correlation and 
                stats['direction_accuracy'] > 0.48):
                valid_factors.append((factor_name, stats))
        
        # 按相关性绝对值排序
        valid_factors.sort(key=lambda x: abs(x[1]['correlation']), reverse=True)
        
        # 选择前top_n个因子
        selected_factors = valid_factors[:top_n]
        selected_factor_names = [f[0] for f in selected_factors]
        
        print(f"✅ 选择了{len(selected_factor_names)}个最佳因子:")
        print("排名  因子名称                      相关性     方向准确性    因子组别")
        print("-" * 70)
        for i, (factor_name, stats) in enumerate(selected_factors[:15]):  # 只显示前15个
            print(f"{i+1:2d}.  {factor_name:<25} {stats['correlation']:8.4f}  {stats['direction_accuracy']:8.1%}    {stats['factor_group']}")
        
        if len(selected_factors) > 15:
            print(f"... 还有{len(selected_factors) - 15}个因子")
        
        # 按组别统计选中的因子
        selected_groups = {}
        for factor_name, stats in selected_factors:
            group = stats['factor_group']
            if group not in selected_groups:
                selected_groups[group] = []
            selected_groups[group].append(factor_name)
        
        print(f"\n📊 选中因子的组别分布:")
        for group, factors in selected_groups.items():
            print(f"  {group:<20}: {len(factors)} 个因子")
        
        # 返回选中的因子数据
        selected_factor_df = factor_df[selected_factor_names]
        
        print(f"\n🎯 因子选择完成:")
        print(f"  原始因子数: {len(factor_df.columns)}")
        print(f"  选中因子数: {len(selected_factor_df.columns)}")
        print(f"  数据维度: {selected_factor_df.shape}")
        
        return selected_factor_df, selected_factors


# 在主执行部分添加新的选项
if __name__ == "__main__":
    print("🔧 修复版qlib因子机器学习管道")
    print("主要改进:")
    print("1. ✅ 修复了语法错误")
    print("2. ✅ 添加了Alpha158/Alpha360因子计算")
    print("3. ✅ 优化了数据处理流程")
    print("4. ✅ 使用简单的GRU模型")
    print("5. ✅ 完整的训练和评估流程")
    print("6. ✅ 新增预测结果诊断功能")
    print("7. ✅ 修复标签创建逻辑问题")
    print("8. ✅ 添加完整的回测系统")
    print("9. ✅ 详细的样本分布分析")
    print("-" * 70)
    
    # 数据路径
    pkl_path = '/home/<USER>'
    # pkl_path = 'data/final_BTC-USDT_1m_20230821_20250412.pkl'
    
    if os.path.exists(pkl_path):
        print(f"📁 加载数据: {pkl_path}")
        data = pd.read_pickle(pkl_path)
        
        # 为了快速测试，只使用部分数据
        # data = data.iloc[-10000:]  # 使用最后10000行数据
        
        print(f"📊 数据信息: {len(data)} 行, 时间范围: {data.index[0]} 到 {data.index[-1]}")
        
        # 选择模型类型
        print("\n🔄 选择模型类型:")
        print("1. GRU (循环神经网络)")
        print("2. Transformer (注意力机制)")
        print("3. XGBoost (梯度提升树)")
        print("4. 比较所有模型")
        
        # 这里默认运行所有模型进行比较
        model_configs = [
            ('gru', {'hidden_size': 64, 'num_layers': 1, 'dropout': 0.1}),
            ('transformer', {'d_model': 64, 'nhead': 8, 'num_layers': 2, 'dropout': 0.1}),
        ]
        
        # 如果XGBoost可用，也添加到比较中
        if XGBOOST_AVAILABLE:
            model_configs.append(('xgboost', {'learning_rate': 0.1, 'max_depth': 6, 'n_estimators': 100}))
        
        all_results = {}
        
        for model_type, model_params in model_configs:
            print(f"\n{'='*70}")
            print(f"🚀 训练 {model_type.upper()} 模型")
            print(f"{'='*70}")
            
            try:
                results = main_training_pipeline(data, model_type=model_type, **model_params)
                
                if results:
                    all_results[model_type] = results
                    print(f"\n✅ {model_type.upper()} 模型训练完成！")
                    if model_type != 'xgboost':
                        print(f"📈 模型已保存: best_{model_type}_model.pth")
                else:
                    print(f"\n❌ {model_type.upper()} 模型训练失败")
                    
            except Exception as e:
                print(f"\n❌ {model_type.upper()} 模型训练出错: {e}")
                continue
        
        # 模型比较
        if len(all_results) > 1:
            print(f"\n{'='*70}")
            print("📊 模型性能比较")
            print(f"{'='*70}")
            
            print("\n📈 30分钟收益率预测性能 (训练目标):")
            print("-" * 50)
            
            for model_name, results in all_results.items():
                predictions = results['predictions']
                actuals = results['actuals']
                
                # 30分钟预测效果
                pred_30min = predictions[:, 0]
                actual_30min = actuals[:, 0]
                
                mse = np.mean((pred_30min - actual_30min) ** 2)
                corr = np.corrcoef(pred_30min, actual_30min)[0, 1] if len(pred_30min) > 1 else 0
                direction_acc = np.mean(np.sign(pred_30min) == np.sign(actual_30min))
                
                print(f"  {model_name.upper()}: MSE={mse:.4f}, Corr={corr:.4f}, Dir_Acc={direction_acc:.2%}")
            
            print("\n📊 用30分钟预测评估其他时间段:")
            print("-" * 50)
            
            evaluation_names = ['10分钟', '60分钟', '120分钟']
            
            for i, eval_name in enumerate(evaluation_names):
                print(f"\n{eval_name}收益率评估:")
                for model_name, results in all_results.items():
                    eval_predictions = results['eval_predictions']
                    eval_targets = results['eval_targets']
                    
                    actual_eval = eval_targets[:, i]
                    
                    mse = np.mean((eval_predictions - actual_eval) ** 2)
                    corr = np.corrcoef(eval_predictions, actual_eval)[0, 1] if len(eval_predictions) > 1 else 0
                    direction_acc = np.mean(np.sign(eval_predictions) == np.sign(actual_eval))
                    
                    print(f"  {model_name.upper()}: MSE={mse:.4f}, Corr={corr:.4f}, Dir_Acc={direction_acc:.2%}")
        
        elif len(all_results) == 1:
            model_name = list(all_results.keys())[0]
            print(f"\n✅ {model_name.upper()} 模型训练完成！")
        else:
            print("\n❌ 所有模型训练都失败了")
    else:
        print(f"❌ 数据文件未找到: {pkl_path}")
        print("请确保数据文件存在")
