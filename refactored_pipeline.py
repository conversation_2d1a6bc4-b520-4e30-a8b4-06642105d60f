#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的机器学习管道 - 结构化、高效、可维护
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from sklearn.preprocessing import RobustScaler
from tqdm.autonotebook import tqdm
import warnings
import pickle

warnings.filterwarnings('ignore')
class MultiScaleTCNTransformer(nn.Module):
    """
    多尺度时间卷积Transformer模型
    结合TCN的局部特征提取和Transformer的全局依赖建模
    """
    
    def __init__(self, input_size, hidden_size=128, num_heads=8, num_layers=6, 
                 dropout=0.1, tcn_channels=[64, 128, 256], kernel_sizes=[3, 5, 7]):
        super(MultiScaleTCNTransformer, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.num_layers = num_layers
        
        # 输入投影层
        self.input_projection = nn.Linear(input_size, hidden_size)
        
        # 多尺度时间卷积网络
        self.tcn_branches = nn.ModuleList()
        for i, (channels, kernel_size) in enumerate(zip(tcn_channels, kernel_sizes)):
            branch = nn.Sequential(
                nn.Conv1d(hidden_size, channels, kernel_size, padding=kernel_size//2),
                nn.BatchNorm1d(channels),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Conv1d(channels, channels, kernel_size, padding=kernel_size//2),
                nn.BatchNorm1d(channels),
                nn.ReLU(),
                nn.Dropout(dropout)
            )
            self.tcn_branches.append(branch)
        
        # 特征融合层
        total_tcn_channels = sum(tcn_channels)
        self.tcn_fusion = nn.Sequential(
            nn.Linear(total_tcn_channels, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 位置编码
        self.pos_encoding = self._create_positional_encoding(512, hidden_size)
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 自适应池化
        self.adaptive_pool = nn.AdaptiveAvgPool1d(1)
        
        # 预测头 - 简化为单时间段预测
        self.prediction_heads = nn.ModuleDict({
            'return_120min': self._create_prediction_head(hidden_size, 1),
            'direction_120min': self._create_prediction_head(hidden_size, 1),
        })
        
        # 初始化权重
        self._initialize_weights()
    
    def _create_positional_encoding(self, max_len, d_model):
        """创建位置编码"""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return nn.Parameter(pe.unsqueeze(0), requires_grad=False)
    
    def _create_prediction_head(self, input_size, output_size):
        """创建回归预测头"""
        return nn.Sequential(
            nn.Linear(input_size, input_size // 2),
            nn.LayerNorm(input_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_size // 2, input_size // 4),
            nn.LayerNorm(input_size // 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_size // 4, output_size)
        )
    
    def _create_classification_head(self, input_size, num_classes):
        """创建分类预测头"""
        return nn.Sequential(
            nn.Linear(input_size, input_size // 2),
            nn.LayerNorm(input_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_size // 2, input_size // 4),
            nn.LayerNorm(input_size // 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_size // 4, num_classes)
        )
    
    def _initialize_weights(self):
        """初始化权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if len(param.shape) >= 2:
                    nn.init.xavier_uniform_(param, gain=0.02)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def forward(self, x):
        """前向传播"""
        batch_size, seq_len, _ = x.shape
        
        # 1. 输入投影
        x = self.input_projection(x)  # (batch, seq_len, hidden_size)
        
        # 2. 多尺度TCN特征提取
        x_tcn = x.transpose(1, 2)  # (batch, hidden_size, seq_len)
        tcn_outputs = []
        for tcn_branch in self.tcn_branches:
            tcn_out = tcn_branch(x_tcn)  # (batch, channels, seq_len)
            tcn_outputs.append(tcn_out)
        
        # 拼接多尺度特征
        tcn_concat = torch.cat(tcn_outputs, dim=1)  # (batch, total_channels, seq_len)
        tcn_concat = tcn_concat.transpose(1, 2)  # (batch, seq_len, total_channels)
        
        # 融合TCN特征
        tcn_fused = self.tcn_fusion(tcn_concat)  # (batch, seq_len, hidden_size)
        
        # 3. 添加位置编码
        pos_enc = self.pos_encoding[:, :seq_len, :].to(x.device)
        x_with_pos = tcn_fused + pos_enc
        
        # 4. Transformer编码
        transformer_out = self.transformer_encoder(x_with_pos)  # (batch, seq_len, hidden_size)
        
        # 5. 全局特征聚合
        pooled_features = self.adaptive_pool(transformer_out.transpose(1, 2)).squeeze(-1)
        
        # 6. 简化预测 - 只预测120分钟
        predictions = []
        # 按照标签创建的顺序获取预测结果
        task_order = ['return_120min', 'direction_120min']
        
        for task_name in task_order:
            pred = self.prediction_heads[task_name](pooled_features)
            predictions.append(pred)
        
        # 拼接所有预测结果
        output = torch.cat(predictions, dim=1)  # (batch_size, 2)
        
        return output


class FinancialTransformerXL(nn.Module):
    """
    金融专用Transformer-XL模型
    支持超长序列建模和记忆机制
    """
    
    def __init__(self, input_size, d_model=256, n_head=8, n_layers=12, 
                 d_ff=1024, dropout=0.1, mem_len=150, ext_len=0):
        super(FinancialTransformerXL, self).__init__()
        
        self.d_model = d_model
        self.n_head = n_head
        self.n_layers = n_layers
        self.mem_len = mem_len
        self.ext_len = ext_len
        
        # 输入嵌入
        self.input_embedding = nn.Sequential(
            nn.Linear(input_size, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
        # 相对位置编码
        self.r_w_bias = nn.Parameter(torch.zeros(n_head, d_model // n_head))
        self.r_r_bias = nn.Parameter(torch.zeros(n_head, d_model // n_head))
        
        # Transformer-XL层
        self.layers = nn.ModuleList([
            TransformerXLLayer(d_model, n_head, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 6)  # 6个预测目标
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for name, param in self.named_parameters():
            if 'weight' in name and len(param.shape) >= 2:
                nn.init.normal_(param, mean=0.0, std=0.02)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def init_mems(self, batch_size):
        """初始化记忆"""
        if self.mem_len > 0:
            mems = []
            for _ in range(self.n_layers):
                empty = torch.empty(0, dtype=torch.float)
                mems.append(empty)
            return mems
        else:
            return None
    
    def forward(self, x, mems=None):
        """前向传播"""
        batch_size, seq_len, _ = x.shape
        
        # 输入嵌入
        hidden = self.input_embedding(x)
        
        # 初始化记忆
        if mems is None:
            mems = self.init_mems(batch_size)
        
        # 通过Transformer-XL层
        new_mems = []
        for i, layer in enumerate(self.layers):
            mem = mems[i] if mems else None
            hidden, new_mem = layer(hidden, mem)
            new_mems.append(new_mem)
        
        # 输出预测
        output = self.output_projection(hidden[:, -1, :])  # 取最后一个时间步
        
        # 只返回预测输出，忽略记忆状态（为了与其他模型保持一致）
        return output


class TransformerXLLayer(nn.Module):
    """Transformer-XL单层"""
    
    def __init__(self, d_model, n_head, d_ff, dropout):
        super(TransformerXLLayer, self).__init__()
        
        self.d_model = d_model
        self.n_head = n_head
        
        # 多头注意力
        self.attention = RelativeMultiHeadAttention(d_model, n_head, dropout)
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
        
        # LayerNorm
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
    def forward(self, hidden, mem=None):
        """前向传播"""
        # 拼接记忆
        if mem is not None and mem.numel() > 0:
            cat_hidden = torch.cat([mem, hidden], dim=1)
        else:
            cat_hidden = hidden
        
        # 自注意力
        attn_out = self.attention(hidden, cat_hidden)
        hidden = self.norm1(hidden + attn_out)
        
        # 前馈网络
        ff_out = self.feed_forward(hidden)
        hidden = self.norm2(hidden + ff_out)
        
        # 更新记忆
        new_mem = cat_hidden.detach()
        
        return hidden, new_mem


class RelativeMultiHeadAttention(nn.Module):
    """相对位置多头注意力"""
    
    def __init__(self, d_model, n_head, dropout):
        super(RelativeMultiHeadAttention, self).__init__()
        
        self.d_model = d_model
        self.n_head = n_head
        self.d_head = d_model // n_head
        
        self.q_linear = nn.Linear(d_model, d_model, bias=False)
        self.k_linear = nn.Linear(d_model, d_model, bias=False)
        self.v_linear = nn.Linear(d_model, d_model, bias=False)
        self.out_linear = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query, key_value):
        """前向传播"""
        batch_size, q_len, _ = query.shape
        kv_len = key_value.shape[1]
        
        # 线性变换
        q = self.q_linear(query).view(batch_size, q_len, self.n_head, self.d_head)
        k = self.k_linear(key_value).view(batch_size, kv_len, self.n_head, self.d_head)
        v = self.v_linear(key_value).view(batch_size, kv_len, self.n_head, self.d_head)
        
        # 转置维度
        q = q.transpose(1, 2)  # (batch, n_head, q_len, d_head)
        k = k.transpose(1, 2)  # (batch, n_head, kv_len, d_head)
        v = v.transpose(1, 2)  # (batch, n_head, kv_len, d_head)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.d_head)
        
        # 应用注意力权重
        attn_weights = torch.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 加权求和
        attn_output = torch.matmul(attn_weights, v)
        
        # 重新整形
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, q_len, self.d_model
        )
        
        # 输出投影
        output = self.out_linear(attn_output)
        
        return output

# ==============================================================================
# 1. 配置模块 (Config)
# ==============================================================================
class Config:
    """集中管理所有超参数和配置"""
    # --- 数据路径 ---
    # DATA_PATH = 'data/final_BTC-USDT_1m_20230821_20250412.pkl'
    DATA_PATH = 'BTC_USDT_1m_20241228_20250626.pkl'
    MODEL_SAVE_PATH = 'best_refactored_model.pth'
    RESULTS_SAVE_PATH = 'refactored_training_results.pkl'

    # --- 因子缓存配置 ---
    ENABLE_FACTOR_CACHE = True  # 是否启用因子缓存
    FACTOR_CACHE_PATH = 'cached_factors.pkl'  # 因子缓存文件路径
    FORCE_RECALCULATE_FACTORS = False  # 是否强制重新计算因子（忽略缓存）

    # --- 数据分割 ---
    TRAIN_RATIO = 0.7
    VAL_RATIO = 0.15

    # --- 特征工程 ---
    FACTOR_LOOKBACK_WINDOW = 120
    FACTOR_TOP_N = 30
    FACTOR_CORR_THRESHOLD = 0.8

    # --- 标签生成 ---
    TARGET_PERIODS = [120]  # 简化为只预测120分钟（2小时）

    # --- 数据管道 ---
    SEQUENCE_LENGTH = 120
    
    # --- 训练参数 (升级版) ---
    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
    EPOCHS = 1            # 增加训练轮数解决收敛问题
    BATCH_SIZE = 64         # 调整批次大小提高稳定性
    LEARNING_RATE = 3e-4    # 降低学习率提高收敛性
    PATIENCE = 25           # 增加耐心避免过早停止
    NUM_WORKERS = 4

    # --- 流程控制 (升级版) ---
    MODEL_NAME = 'MultiScaleTCNTransformer' # 先使用稳定的模型: 'MultiScaleTCNTransformer', 'FinancialTransformerXL', 'WaveNetForFinance'
    MODEL_HIDDEN_SIZE = 256  # 大幅增加模型容量 (从128到256)
    MODEL_NUM_LAYERS = 4     # 优化层数 (从6到4，避免过拟合)
    MODEL_NUM_HEADS = 8      # 保持多头注意力
    USE_ADVANCED_FACTORS = True
    USE_INTERACTION_FACTORS = False
    
    # --- 损失函数选择 ---
    LOSS_TYPE = 'profit_focused'  # 可选: 'profit_focused', 'trading_optimized', 'traditional'

    # --- 模型参数 (升级版) ---
    MODEL_HIDDEN_SIZE = 256  # 大幅增加隐藏层大小
    MODEL_NUM_LAYERS = 4     # 优化层数
    MODEL_DROPOUT = 0.15     # 增加dropout防止过拟合
    
    # --- 模型参数 (Attention-based models) ---
    MODEL_NUM_HEADS = 8      # 增加注意力头数
    
    # --- 模型参数 (MultiScaleTCNTransformer升级版) ---
    TCN_CHANNELS = [128, 256, 512]    # 大幅增加TCN通道数
    TCN_KERNEL_SIZES = [3, 5, 7, 9]   # 增加更多尺度的卷积核
    
    # --- 模型参数 (FinancialTransformerXL) ---
    TRANSFORMER_D_MODEL = 256
    TRANSFORMER_D_FF = 1024
    TRANSFORMER_MEM_LEN = 150
    
    # --- 模型参数 (WaveNet) ---
    WAVENET_RESIDUAL_CHANNELS = 64
    WAVENET_DILATION_CHANNELS = 64
    WAVENET_NUM_STACKS = 3

    # --- 新增：价量配合突破策略参数 ---
    BREAKOUT_MA_PERIOD = 60
    BREAKOUT_VOLUME_MA_PERIOD = 20
    BREAKOUT_VOLUME_MULTIPLIER = 1.5

# ==============================================================================
# 2. 因子缓存管理器
# ==============================================================================
import os
import hashlib

class FactorCacheManager:
    """因子缓存管理器 - 避免重复计算因子"""
    
    def __init__(self, config):
        self.config = config
        self.cache_path = config.FACTOR_CACHE_PATH
        self.enable_cache = config.ENABLE_FACTOR_CACHE
        self.force_recalculate = config.FORCE_RECALCULATE_FACTORS
        
    def _generate_cache_key(self, df):
        """生成缓存键，基于数据的基本特征"""
        # 使用数据的基本信息生成缓存键
        data_info = {
            'shape': df.shape,
            'start_time': str(df.index[0]),
            'end_time': str(df.index[-1]),
            'columns': list(df.columns),
            'factor_lookback_window': self.config.FACTOR_LOOKBACK_WINDOW,
            'use_advanced_factors': self.config.USE_ADVANCED_FACTORS,
            'use_interaction_factors': self.config.USE_INTERACTION_FACTORS
        }
        
        # 生成MD5哈希作为缓存键
        cache_key = hashlib.md5(str(data_info).encode()).hexdigest()
        return cache_key
        
    def _save_factors_to_cache(self, factors_df, cache_key, df):
        """保存因子到缓存文件"""
        try:
            cache_data = {
                'cache_key': cache_key,
                'factors_df': factors_df,
                'data_info': {
                    'shape': df.shape,
                    'start_time': str(df.index[0]),
                    'end_time': str(df.index[-1]),
                    'columns': list(df.columns)
                },
                'config_info': {
                    'factor_lookback_window': self.config.FACTOR_LOOKBACK_WINDOW,
                    'use_advanced_factors': self.config.USE_ADVANCED_FACTORS,
                    'use_interaction_factors': self.config.USE_INTERACTION_FACTORS
                },
                'timestamp': pd.Timestamp.now()
            }
            
            with open(self.cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
                
            print(f"✅ 因子已保存到缓存: {self.cache_path}")
            print(f"   缓存键: {cache_key}")
            print(f"   因子数量: {factors_df.shape[1]}")
            print(f"   数据行数: {factors_df.shape[0]}")
            
        except Exception as e:
            print(f"⚠️ 警告: 保存因子缓存失败: {e}")
            
    def _load_factors_from_cache(self, cache_key):
        """从缓存文件加载因子"""
        try:
            if not os.path.exists(self.cache_path):
                print(f"📝 缓存文件不存在: {self.cache_path}")
                return None
                
            with open(self.cache_path, 'rb') as f:
                cache_data = pickle.load(f)
                
            # 验证缓存键是否匹配
            if cache_data.get('cache_key') != cache_key:
                print("📝 缓存键不匹配，数据可能已更改")
                return None
                
            factors_df = cache_data['factors_df']
            
            print(f"✅ 从缓存加载因子成功: {self.cache_path}")
            print(f"   缓存时间: {cache_data.get('timestamp', '未知')}")
            print(f"   因子数量: {factors_df.shape[1]}")
            print(f"   数据行数: {factors_df.shape[0]}")
            
            return factors_df
            
        except Exception as e:
            print(f"⚠️ 警告: 加载因子缓存失败: {e}")
            return None
            
    def get_or_calculate_factors(self, df):
        """获取或计算因子 - 主要接口函数"""
        print("\n🔧 因子缓存管理器启动...")
        
        if not self.enable_cache:
            print("📝 因子缓存已禁用，直接计算因子")
            return self._calculate_factors(df)
            
        # 生成缓存键
        cache_key = self._generate_cache_key(df)
        
        # 如果强制重新计算，跳过缓存加载
        if self.force_recalculate:
            print("🔄 强制重新计算因子（忽略缓存）")
            factors_df = self._calculate_factors(df)
            self._save_factors_to_cache(factors_df, cache_key, df)
            return factors_df
            
        # 尝试从缓存加载
        print("📂 尝试从缓存加载因子...")
        factors_df = self._load_factors_from_cache(cache_key)
        
        if factors_df is not None:
            # 验证缓存数据的完整性
            if self._validate_cached_factors(factors_df, df):
                print("✅ 使用缓存的因子数据")
                return factors_df
            else:
                print("⚠️ 缓存数据验证失败，重新计算因子")
                
        # 缓存不可用，重新计算因子
        print("🔄 缓存不可用，开始计算因子...")
        factors_df = self._calculate_factors(df)
        
        # 保存到缓存
        self._save_factors_to_cache(factors_df, cache_key, df)
        
        return factors_df
        
    def _validate_cached_factors(self, factors_df, df):
        """验证缓存因子数据的完整性"""
        try:
            # 检查基本形状
            if len(factors_df) == 0:
                print("   验证失败: 因子数据为空")
                return False
                
            # 检查时间索引对齐
            if not factors_df.index.equals(df.index):
                # 检查是否有重叠
                overlap = factors_df.index.intersection(df.index)
                if len(overlap) < len(df.index) * 0.9:  # 至少90%重叠
                    print(f"   验证失败: 时间索引重叠度不足 ({len(overlap)}/{len(df.index)})")
                    return False
                    
            # 检查是否有过多的NaN
            nan_ratio = factors_df.isnull().sum().sum() / (factors_df.shape[0] * factors_df.shape[1])
            if nan_ratio > 0.5:  # NaN比例超过50%
                print(f"   验证失败: NaN比例过高 ({nan_ratio:.1%})")
                return False
                
            print("   验证通过: 缓存因子数据完整")
            return True
            
        except Exception as e:
            print(f"   验证失败: {e}")
            return False
            
    def _calculate_factors(self, df):
        """计算因子 - 原有的因子计算逻辑"""
        import time
        
        print("🔧 开始计算因子...")
        feature_start_time = time.time()
        factor_list = []

        # a. 基础因子 (如果需要)
        # print("  a. 计算基础因子 (EnhancedFactorCalculator)...")
        # efc = EnhancedFactorCalculator()
        # base_factors = efc.calculate_all_factors(df)
        # factor_list.append(base_factors)

        # b. 高级因子 (可选)
        if self.config.USE_ADVANCED_FACTORS:
            print("  b. 计算高级因子 (AdvancedFactorGenerator)...")
            factor_calc_start = time.time()
            afg = AdvancedFactorGenerator(lookback_window=self.config.FACTOR_LOOKBACK_WINDOW)
            advanced_factors = afg.generate_all_factors(df)
            factor_list.append(advanced_factors)
            print(f"    高级因子生成完成，耗时: {time.time() - factor_calc_start:.2f}秒")

        # c. 市场状态特征 (如果需要)
        # if hasattr(self.config, 'USE_MARKET_STATE_FEATURES') and self.config.USE_MARKET_STATE_FEATURES:
        #     print("  c. 计算市场状态特征...")
        #     # 添加市场状态特征计算逻辑
        #     pass
        
        # 合并所有因子
        if factor_list:
            all_factors_df = pd.concat(factor_list, axis=1)
        else:
            # 如果没有启用任何因子，创建一些基本因子
            print("  警告: 没有启用任何因子计算，创建基本因子...")
            basic_factors = self._create_basic_factors(df)
            all_factors_df = basic_factors

        # d. 交互因子 (可选)
        # if self.config.USE_INTERACTION_FACTORS:
        #     print("  d. 生成交互因子...")
        #     # 添加交互因子生成逻辑
        #     pass
        
        feature_total_time = time.time() - feature_start_time
        print(f"✅ 因子计算完成: {all_factors_df.shape[1]} 个因子，耗时: {feature_total_time:.2f}秒")
        
        return all_factors_df
        
    def _create_basic_factors(self, df):
        """创建基本因子作为后备方案"""
        print("    创建基本技术指标因子...")
        
        factors = {}
        periods = [5, 10, 20, 60]
        
        for period in periods:
            min_periods = max(1, period // 2)
            factors[f'ma_{period}'] = df['close'].rolling(period, min_periods=min_periods).mean()
            factors[f'return_{period}'] = df['close'].pct_change(period)
            factors[f'std_{period}'] = df['close'].rolling(period, min_periods=min_periods).std()
            factors[f'high_ratio_{period}'] = df['high'].rolling(period, min_periods=min_periods).max() / df['close']
            factors[f'low_ratio_{period}'] = df['low'].rolling(period, min_periods=min_periods).min() / df['close']
        
        # 即时因子
        factors['price_change'] = df['close'].pct_change()
        factors['high_low_ratio'] = df['high'] / (df['low'] + 1e-8)
        factors['close_open_ratio'] = df['close'] / (df['open'] + 1e-8)
        
        if 'volume' in df.columns:
            factors['volume_change'] = df['volume'].pct_change()
            factors['price_volume'] = df['close'] * df['volume']
            for period in [5, 10, 20]:
                min_periods = max(1, period // 2)
                factors[f'volume_ma_{period}'] = df['volume'].rolling(period, min_periods=min_periods).mean()
        
        factors_df = pd.DataFrame(factors, index=df.index)
        
        # 数据清理
        factors_df = factors_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return factors_df
        
    def clear_cache(self):
        """清除缓存文件"""
        try:
            if os.path.exists(self.cache_path):
                os.remove(self.cache_path)
                print(f"✅ 缓存文件已清除: {self.cache_path}")
            else:
                print(f"📝 缓存文件不存在: {self.cache_path}")
        except Exception as e:
            print(f"⚠️ 清除缓存失败: {e}")

# ==============================================================================
# 3. 核心功能模块 (因子、模型、评估等)
# ==============================================================================

class TimeSeriesSafeDataSplitter:
    """时间序列安全的数据分割器"""
    def __init__(self, train_ratio=0.7, val_ratio=0.15):
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = 1.0 - train_ratio - val_ratio
        
        if abs(train_ratio + val_ratio + self.test_ratio - 1.0) > 1e-6:
            raise ValueError("数据分割比例之和必须等于1.0")
    
    def split_data(self, data):
        """严格按时间顺序分割数据"""
        if not isinstance(data.index, pd.DatetimeIndex):
            print("警告: 数据索引不是DatetimeIndex，将按行号分割")
        
        n = len(data)
        train_end = int(n * self.train_ratio)
        val_end = int(n * (self.train_ratio + self.val_ratio))
        
        # 时间序列分割
        train_data = data.iloc[:train_end].copy()
        val_data = data.iloc[train_end:val_end].copy()
        test_data = data.iloc[val_end:].copy()
        
        print(f"📊 时间序列数据分割完成:")
        print(f"  训练集: {len(train_data):,} 行 ({len(train_data)/n:.1%})")
        print(f"  验证集: {len(val_data):,} 行 ({len(val_data)/n:.1%})")
        print(f"  测试集: {len(test_data):,} 行 ({len(test_data)/n:.1%})")
        
        return {
            'train': train_data,
            'validation': val_data,
            'test': test_data
        }
    
    def get_split_info(self, data):
        """获取数据分割的详细信息"""
        splits = self.split_data(data)
        
        info = {
            'total_size': len(data),
            'train_info': {
                'size': len(splits['train']),
                'ratio': len(splits['train']) / len(data),
                'start_time': splits['train'].index[0] if len(splits['train']) > 0 else None,
                'end_time': splits['train'].index[-1] if len(splits['train']) > 0 else None
            },
            'validation_info': {
                'size': len(splits['validation']),
                'ratio': len(splits['validation']) / len(data),
                'start_time': splits['validation'].index[0] if len(splits['validation']) > 0 else None,
                'end_time': splits['validation'].index[-1] if len(splits['validation']) > 0 else None
            },
            'test_info': {
                'size': len(splits['test']),
                'ratio': len(splits['test']) / len(data),
                'start_time': splits['test'].index[0] if len(splits['test']) > 0 else None,
                'end_time': splits['test'].index[-1] if len(splits['test']) > 0 else None
            }
        }
        
        return info
    
    def validate_split_integrity(self, data):
        """验证数据分割的完整性，确保没有数据泄露"""
        splits = self.split_data(data)
        
        # 检查时间顺序
        train_end_time = splits['train'].index[-1]
        val_start_time = splits['validation'].index[0]
        val_end_time = splits['validation'].index[-1]
        test_start_time = splits['test'].index[0]
        
        if train_end_time >= val_start_time:
            print("❌ 错误: 训练集和验证集时间重叠")
            return False
        
        if val_end_time >= test_start_time:
            print("❌ 错误: 验证集和测试集时间重叠")
            return False
        
        # 检查数据连续性
        expected_val_start = splits['train'].index[-1]
        expected_test_start = splits['validation'].index[-1]
        
        print("✅ 数据分割完整性验证通过")
        print(f"  训练集结束时间: {train_end_time}")
        print(f"  验证集开始时间: {val_start_time}")
        print(f"  验证集结束时间: {val_end_time}")
        print(f"  测试集开始时间: {test_start_time}")
        
        return True


class AdvancedFactorGenerator:
    """高级因子生成器"""
    def __init__(self, lookback_window=120, prediction_horizon=60):
        self.lookback_window = lookback_window
        self.prediction_horizon = prediction_horizon
        
    def generate_all_factors(self, data):
        """生成所有高级因子 - 添加性能监控"""
        import time
        
        print("🔧 开始生成高级因子...")
        start_time = time.time()
        
        all_factors = {}
        
        # 1. 微观结构因子
        print("  计算微观结构因子...")
        factor_start = time.time()
        microstructure_factors = self.generate_microstructure_factors(data)
        all_factors.update(microstructure_factors)
        print(f"    微观结构因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 2. 时序动量因子
        print("  计算时序动量因子...")
        factor_start = time.time()
        temporal_momentum_factors = self.generate_temporal_momentum_factors(data)
        all_factors.update(temporal_momentum_factors)
        print(f"    时序动量因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 3. 价量关系因子
        print("  计算价量关系因子...")
        factor_start = time.time()
        price_volume_factors = self.generate_price_volume_factors(data)
        all_factors.update(price_volume_factors)
        print(f"    价量关系因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 4. 波动率因子
        print("  计算波动率因子...")
        factor_start = time.time()
        volatility_factors = self.generate_volatility_factors(data)
        all_factors.update(volatility_factors)
        print(f"    波动率因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 5. 创建因子DataFrame
        factor_df = pd.DataFrame(all_factors, index=data.index)
        
        # 6. 数据清理
        factor_df = self._clean_factors(factor_df)
        
        total_time = time.time() - start_time
        print(f"✅ 生成 {len(factor_df.columns)} 个高级因子，总耗时: {total_time:.2f}秒")
        return factor_df
    
    def generate_microstructure_factors(self, data):
        """生成微观结构因子"""
        factors = {}
        
        # 1. 价量弹性系数
        factors['price_volume_elasticity'] = self._calculate_price_volume_elasticity(data)
        
        # 2. 流动性指纹
        factors['liquidity_fingerprint'] = self._calculate_liquidity_fingerprint(data)
        
        # 3. 高频反转强度
        factors['intraday_reversal_strength'] = self._calculate_reversal_strength(data)
        
        # 4. 订单流失衡
        factors['order_flow_imbalance'] = self._calculate_order_flow_imbalance(data)
        
        # 5. 价格冲击
        factors['price_impact'] = self._calculate_price_impact(data)
        
        return factors
    
    def generate_temporal_momentum_factors(self, data):
        """生成时序动量因子"""
        factors = {}
        
        # 1. 分段动量
        factors['segment_momentum_short'] = self._calculate_segment_momentum(data, 20)
        factors['segment_momentum_medium'] = self._calculate_segment_momentum(data, 40)
        factors['segment_momentum_long'] = self._calculate_segment_momentum(data, 60)
        
        # 2. 动量加速度
        factors['momentum_acceleration'] = self._calculate_momentum_acceleration(data)
        
        # 3. 趋势持续性
        factors['trend_persistence'] = self._calculate_trend_persistence(data)
        
        # 4. 波动率制度转换
        factors['volatility_regime_shift'] = self._calculate_volatility_regime_shift(data)
        
        return factors
    
    def generate_price_volume_factors(self, data):
        """生成价量关系因子"""
        factors = {}
        
        if 'volume' not in data.columns:
            print("    警告: 无成交量数据，跳过价量关系因子")
            return factors
        
        # 1. 价量相关性
        factors['price_volume_corr_short'] = self._calculate_price_volume_correlation(data, 10)
        factors['price_volume_corr_medium'] = self._calculate_price_volume_correlation(data, 30)
        factors['price_volume_corr_long'] = self._calculate_price_volume_correlation(data, 60)
        
        # 2. 成交量加权平均价格偏差
        factors['vwap_deviation'] = self._calculate_vwap_deviation(data)
        
        # 3. 成交量突破
        factors['volume_breakout'] = self._calculate_volume_breakout(data)
        
        # 4. 价量同步性
        factors['price_volume_sync'] = self._calculate_price_volume_sync(data)
        
        return factors
    
    def generate_volatility_factors(self, data):
        """生成波动率因子"""
        factors = {}
        
        # 1. 已实现波动率
        factors['realized_volatility_5m'] = self._calculate_realized_volatility(data, 5)
        factors['realized_volatility_15m'] = self._calculate_realized_volatility(data, 15)
        factors['realized_volatility_30m'] = self._calculate_realized_volatility(data, 30)
        factors['realized_volatility_60m'] = self._calculate_realized_volatility(data, 60)
        
        # 2. 波动率偏度
        factors['volatility_skewness'] = self._calculate_volatility_skewness(data)
        
        # 3. 波动率聚类
        factors['volatility_clustering'] = self._calculate_volatility_clustering(data)
        
        # 4. 高低价波动率
        factors['high_low_volatility'] = self._calculate_high_low_volatility(data)
        
        return factors
    
    def _calculate_price_volume_elasticity(self, data):
        """计算价量弹性系数 - 向量化优化版本"""
        price_change = data['close'].pct_change()
        
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        volume_change = data['volume'].pct_change()
        
        # 向量化计算：使用rolling相关性，一次性计算所有窗口
        window_size = 20
        min_periods = FutureLeakageValidator.validate_rolling_operations(window_size, 10)
        
        # 直接使用pandas的滚动相关性计算，避免Python循环
        elasticity = price_change.rolling(window=window_size, min_periods=min_periods).corr(volume_change)
        
        # 处理NaN和无穷大值
        elasticity = elasticity.replace([np.inf, -np.inf], 0).fillna(0)
        
        # 验证无未来信息泄露
        FutureLeakageValidator.validate_no_future_leakage(elasticity, data.index, 'price_volume_elasticity')
        
        return elasticity
    
    def _calculate_liquidity_fingerprint(self, data):
        """计算流动性指纹 - 优化版本确保无未来信息泄露"""
        # 基于价格冲击和成交量的关系
        price_impact = (data['high'] - data['low']) / (data['close'] + 1e-8)
        
        if 'volume' in data.columns:
            # 确保rolling窗口参数正确
            volume_window = 60
            volume_min_periods = FutureLeakageValidator.validate_rolling_operations(volume_window, 30)
            volume_normalized = data['volume'] / (data['volume'].rolling(volume_window, min_periods=volume_min_periods).mean() + 1e-8)
            liquidity_score = price_impact / (volume_normalized + 1e-8)
        else:
            liquidity_score = price_impact
        
        # 平滑处理
        smooth_window = 30
        smooth_min_periods = FutureLeakageValidator.validate_rolling_operations(smooth_window, 15)
        result = liquidity_score.rolling(smooth_window, min_periods=smooth_min_periods).mean()
        result = result.fillna(0)
        
        # 验证无未来信息泄露
        FutureLeakageValidator.validate_no_future_leakage(result, data.index, 'liquidity_fingerprint')
        
        return result
    
    def _calculate_reversal_strength(self, data):
        """计算高频反转强度"""
        # 基于价格在高低点之间的反转模式
        high_roll = data['high'].rolling(30, min_periods=15).max()
        low_roll = data['low'].rolling(30, min_periods=15).min()
        
        price_position = (data['close'] - low_roll) / (high_roll - low_roll + 1e-8)
        
        # 反转强度 = 价格位置的变化率
        reversal_strength = price_position.diff().abs().rolling(10, min_periods=5).mean()
        return reversal_strength.fillna(0)
    
    def _calculate_order_flow_imbalance(self, data):
        """计算订单流失衡 - 向量化优化版本"""
        # 基于价格变化和成交量的关系
        price_change = data['close'].diff()
        
        if 'volume' in data.columns:
            # 向量化计算上涨和下跌时的成交量
            buy_volume = np.where(price_change > 0, data['volume'], 0)
            sell_volume = np.where(price_change < 0, data['volume'], 0)
            
            # 使用rolling sum计算移动平均成交量
            window_size = 20
            min_periods = FutureLeakageValidator.validate_rolling_operations(window_size, 10)
            
            buy_volume_series = pd.Series(buy_volume, index=data.index)
            sell_volume_series = pd.Series(sell_volume, index=data.index)
            
            buy_volume_ma = buy_volume_series.rolling(window_size, min_periods=min_periods).sum()
            sell_volume_ma = sell_volume_series.rolling(window_size, min_periods=min_periods).sum()
            
            # 计算失衡度
            imbalance = (buy_volume_ma - sell_volume_ma) / (buy_volume_ma + sell_volume_ma + 1e-8)
        else:
            # 无成交量数据时，使用价格动量
            window_size = 20
            min_periods = FutureLeakageValidator.validate_rolling_operations(window_size, 10)
            imbalance = price_change.rolling(window_size, min_periods=min_periods).mean()
        
        result = imbalance.fillna(0)
        
        # 验证无未来信息泄露
        FutureLeakageValidator.validate_no_future_leakage(result, data.index, 'order_flow_imbalance')
        
        return result
    
    def _calculate_price_impact(self, data):
        """计算价格冲击"""
        # 价格冲击 = 价格变化幅度 / 成交量
        price_range = data['high'] - data['low']
        
        if 'volume' in data.columns:
            impact = price_range / (data['volume'] + 1e-8)
        else:
            impact = price_range / data['close']
        
        # 标准化
        impact_normalized = impact / impact.rolling(60, min_periods=30).mean()
        return impact_normalized.fillna(1)
    
    def _calculate_segment_momentum(self, data, window):
        """计算分段动量 - 向量化优化版本"""
        # 将窗口分为4个段，计算每段的动量趋势
        segment_size = window // 4
        close_prices = data['close']
        
        # 向量化计算4个分段的收益率
        def calc_segment_momentum_vectorized(series):
            """向量化计算单个窗口的分段动量"""
            if len(series) < window:
                return 0
            
            # 将窗口分为4段，计算每段收益率
            segments = []
            for j in range(4):
                start_idx = j * segment_size
                end_idx = (j + 1) * segment_size
                if end_idx <= len(series):
                    segment_data = series.iloc[start_idx:end_idx]
                    if len(segment_data) > 0:
                        segment_return = (segment_data.iloc[-1] - segment_data.iloc[0]) / (segment_data.iloc[0] + 1e-8)
                        segments.append(segment_return)
            
            # 计算分段动量的趋势（与时间序列的相关性）
            if len(segments) >= 2:
                try:
                    correlation = np.corrcoef(segments, range(len(segments)))[0, 1]
                    return correlation if not pd.isna(correlation) else 0
                except:
                    return 0
            return 0
        
        # 使用rolling apply进行向量化计算
        min_periods = FutureLeakageValidator.validate_rolling_operations(window, segment_size)
        result = close_prices.rolling(window=window, min_periods=min_periods).apply(
            calc_segment_momentum_vectorized, raw=False
        )
        
        # 处理NaN值
        result = result.fillna(0)
        
        # 验证无未来信息泄露
        FutureLeakageValidator.validate_no_future_leakage(result, data.index, f'segment_momentum_{window}')
        
        return result
    
    def _calculate_momentum_acceleration(self, data):
        """计算动量加速度"""
        # 价格动量的二阶导数
        price_momentum = data['close'].pct_change(5)
        acceleration = price_momentum.diff()
        
        # 平滑处理
        result = acceleration.rolling(10, min_periods=5).mean()
        return result.fillna(0)
    
    def _calculate_trend_persistence(self, data):
        """计算趋势持续性 - 向量化优化版本"""
        # 基于价格趋势的持续时间
        price_change = data['close'].diff()
        direction = np.sign(price_change)
        
        # 向量化计算连续同向运动的长度
        # 使用groupby和cumsum的向量化方法
        direction_change = (direction != direction.shift(1)) | (direction == 0)
        direction_groups = direction_change.cumsum()
        
        # 计算每个组内的连续长度
        persistence = direction.groupby(direction_groups).cumcount() + 1
        
        # 当方向为0时，持续性也为0
        persistence = persistence.where(direction != 0, 0)
        
        # 标准化
        window_size = 60
        min_periods = FutureLeakageValidator.validate_rolling_operations(window_size, 30)
        rolling_mean = persistence.rolling(window=window_size, min_periods=min_periods).mean()
        result = persistence / (rolling_mean + 1e-8)
        result = result.fillna(1)
        
        # 验证无未来信息泄露
        FutureLeakageValidator.validate_no_future_leakage(result, data.index, 'trend_persistence')
        
        return result
    
    def _calculate_volatility_regime_shift(self, data):
        """计算波动率制度转换"""
        # 基于波动率的变化检测制度转换
        returns = data['close'].pct_change()
        volatility = returns.rolling(20, min_periods=10).std()
        
        # 波动率的变化率
        vol_change = volatility.pct_change()
        
        # 制度转换信号
        regime_shift = vol_change.rolling(10, min_periods=5).mean()
        return regime_shift.fillna(0)
    
    def _calculate_price_volume_correlation(self, data, window):
        """计算价量相关性"""
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        price_change = data['close'].pct_change()
        volume_change = data['volume'].pct_change()
        
        # 滚动相关性
        correlation = price_change.rolling(window, min_periods=window//2).corr(volume_change)
        return correlation.fillna(0)
    
    def _calculate_vwap_deviation(self, data):
        """计算VWAP偏差"""
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        # 计算VWAP
        vwap = (data['close'] * data['volume']).rolling(30, min_periods=15).sum() / data['volume'].rolling(30, min_periods=15).sum()
        
        # 计算偏差
        deviation = (data['close'] - vwap) / (vwap + 1e-8)
        return deviation.fillna(0)
    
    def _calculate_volume_breakout(self, data):
        """计算成交量突破"""
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        vol_ma = data['volume'].rolling(30, min_periods=15).mean()
        vol_std = data['volume'].rolling(30, min_periods=15).std()
        
        # 突破信号
        breakout = (data['volume'] - vol_ma) / (vol_std + 1e-8)
        return breakout.fillna(0)
    
    def _calculate_price_volume_sync(self, data):
        """计算价量同步性"""
        if 'volume' not in data.columns:
            return pd.Series(0, index=data.index)
        
        price_direction = np.sign(data['close'].diff())
        volume_direction = np.sign(data['volume'].diff())
        
        # 同步性 = 方向一致性
        sync = (price_direction == volume_direction).astype(int)
        result = sync.rolling(20, min_periods=10).mean()
        return result.fillna(0.5)
    
    def _calculate_realized_volatility(self, data, window):
        """计算已实现波动率"""
        returns = data['close'].pct_change()
        realized_vol = returns.rolling(window, min_periods=window//2).std()
        return realized_vol.fillna(0)
    
    def _calculate_volatility_skewness(self, data):
        """计算波动率偏度"""
        returns = data['close'].pct_change()
        skewness = returns.rolling(60, min_periods=30).skew()
        return skewness.fillna(0)
    
    def _calculate_volatility_clustering(self, data):
        """计算波动率聚类"""
        returns = data['close'].pct_change()
        volatility = returns.rolling(10, min_periods=5).std()
        
        # 波动率的自相关
        clustering = volatility.rolling(20, min_periods=10).apply(lambda x: x.autocorr(lag=1))
        return clustering.fillna(0)
    
    def _calculate_high_low_volatility(self, data):
        """计算高低价波动率"""
        # 基于高低价的波动率
        hl_ratio = np.log(data['high'] / data['low'])
        hl_volatility = hl_ratio.rolling(20, min_periods=10).std()
        return hl_volatility.fillna(0)
    
    def _clean_factors(self, factor_df):
        """清理因子数据"""
        print("  清理因子数据...")
        
        # 替换无穷大值
        factor_df = factor_df.replace([np.inf, -np.inf], np.nan)
        
        # 删除全NaN的列
        factor_df = factor_df.dropna(axis=1, how='all')
        
        # 处理异常值 (使用3倍标准差)
        for col in factor_df.columns:
            if factor_df[col].dtype in ['float64', 'float32']:
                mean_val = factor_df[col].mean()
                std_val = factor_df[col].std()
                
                if std_val > 0:
                    # 3倍标准差范围
                    lower_bound = mean_val - 3 * std_val
                    upper_bound = mean_val + 3 * std_val
                    
                    # 限制异常值
                    factor_df[col] = factor_df[col].clip(lower_bound, upper_bound)
        
        # 填充剩余的NaN
        factor_df = factor_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return factor_df


# ==============================================================================
# 6. 技术指标辅助函数
# ==============================================================================

def calculate_rsi(close_series, period):
    """计算RSI指标 - 向量化实现"""
    delta = close_series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=max(1, period//2)).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=max(1, period//2)).mean()
    
    rs = gain / (loss + 1e-8)
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)

# ==============================================================================
# 7. 未来信息泄露验证器
# ==============================================================================

class FutureLeakageValidator:
    """未来信息泄露验证器"""
    
    @staticmethod
    def validate_no_future_leakage(factor_series, data_index, factor_name):
        """
        验证因子计算是否存在未来信息泄露
        
        Args:
            factor_series: 计算得到的因子序列
            data_index: 原始数据的时间索引
            factor_name: 因子名称
            
        Returns:
            bool: True表示无泄露，False表示存在泄露
        """
        # 检查因子值是否依赖于未来数据
        # 这里进行基础的时间索引一致性检查
        if len(factor_series) != len(data_index):
            print(f"  警告: {factor_name} 长度不匹配原始数据")
            return False
            
        # 检查是否存在异常的前向填充
        if factor_series.isna().sum() == 0 and len(factor_series) > 100:
            # 如果前100个值都不是NaN，可能存在前向填充
            first_100_std = factor_series.iloc[:100].std()
            if first_100_std > 0:
                print(f"  注意: {factor_name} 前100个值标准差 {first_100_std:.6f}，请确认无前向填充")
        
        return True
    
    @staticmethod
    def validate_rolling_operations(window_size, min_periods=None):
        """验证滚动窗口操作的参数设置"""
        if min_periods is None:
            min_periods = max(1, window_size // 2)
        
        # 确保min_periods不大于window_size
        min_periods = min(min_periods, window_size)
        
        if min_periods <= 0:
            min_periods = 1
            
        return min_periods

# ==============================================================================
# 8. 增强版因子计算器
# ==============================================================================

class EnhancedFactorCalculator:
    """增强版因子计算器 - 性能优化版本"""
    def __init__(self):
        self.alpha158_factors = []
        self.alpha360_factors = []
        # 添加缓存机制
        self._cache = {}
        self._is_cache_initialized = False
    
    def _precompute_base_series(self, df):
        """预计算所有基础序列，避免重复计算"""
        import time
        
        if self._is_cache_initialized:
            return
        
        print("  预计算基础序列缓存...")
        start_time = time.time()
        
        # 定义所有需要的周期
        all_periods = [1, 2, 3, 4, 5, 6, 10, 15, 20, 30, 60, 120, 240]
        
        # 1. 预计算价格收益率
        self._cache['returns'] = {}
        for period in all_periods:
            self._cache['returns'][period] = df['close'].pct_change(period)
        
        # 2. 预计算移动平均
        self._cache['ma'] = {}
        for period in all_periods:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
            self._cache['ma'][period] = df['close'].rolling(period, min_periods=min_periods).mean()
        
        # 3. 预计算标准差
        self._cache['std'] = {}
        for period in all_periods:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
            self._cache['std'][period] = df['close'].rolling(period, min_periods=min_periods).std()
        
        # 4. 预计算最高价/最低价
        self._cache['max'] = {}
        self._cache['min'] = {}
        for period in all_periods:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
            self._cache['max'][period] = df['high'].rolling(period, min_periods=min_periods).max()
            self._cache['min'][period] = df['low'].rolling(period, min_periods=min_periods).min()
        
        # 5. 预计算EMA
        self._cache['ema'] = {}
        ema_periods = [5, 8, 10, 12, 21, 26, 50, 55, 100]
        for period in ema_periods:
            self._cache['ema'][period] = df['close'].ewm(span=period).mean()
        
        # 6. 预计算RSI
        self._cache['rsi'] = {}
        rsi_periods = [3, 6, 9, 12, 14, 21, 24, 30, 42, 60]
        for period in rsi_periods:
            self._cache['rsi'][period] = calculate_rsi(df['close'], period)
        
        # 7. 预计算成交量相关序列（如果有成交量数据）
        if 'volume' in df.columns:
            self._cache['volume_ma'] = {}
            self._cache['volume_ratio'] = {}
            for period in [5, 10, 15, 20, 30]:
                min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
                vol_ma = df['volume'].rolling(period, min_periods=min_periods).mean()
                self._cache['volume_ma'][period] = vol_ma
                self._cache['volume_ratio'][period] = df['volume'] / (vol_ma + 1e-8)
        
        # 8. 预计算MACD相关序列
        self._cache['macd'] = self._cache['ema'][12] - self._cache['ema'][26]
        self._cache['macd_signal'] = self._cache['macd'].ewm(span=9).mean()
        self._cache['macd_hist'] = self._cache['macd'] - self._cache['macd_signal']
        
        # 9. 预计算布林带相关
        self._cache['bb_position'] = {}
        for period in [10, 20, 30]:
            if period in self._cache['ma'] and period in self._cache['std']:
                bb_position = (df['close'] - self._cache['ma'][period]) / (self._cache['std'][period] + 1e-8)
                self._cache['bb_position'][period] = bb_position
        
        self._is_cache_initialized = True
        cache_time = time.time() - start_time
        print(f"    基础序列缓存完成，耗时: {cache_time:.2f}秒")
        
    def calculate_alpha158_factors(self, df):
        """计算Alpha158因子集 - 向量化优化版本"""
        print("🔢 计算Alpha158因子集...")
        
        # 确保缓存已初始化
        self._precompute_base_series(df)
        
        factors = {}
        
        # 基础价格收益率因子 (1-10) - 使用缓存
        periods = [1, 2, 3, 4, 5, 6, 10, 15, 20, 30]
        for period in periods:
            if period in self._cache['returns']:
                factors[f'alpha_close_return_{period}'] = self._cache['returns'][period]
        
        # 移动平均因子 (11-25) - 使用缓存
        ma_periods = [5, 10, 15, 20, 30, 60, 120]
        for period in ma_periods:
            if period in self._cache['ma']:
                factors[f'alpha_ma_{period}'] = self._cache['ma'][period]
        
        # 价格相对移动平均 (26-35) - 使用缓存
        for period in [5, 10, 15, 20, 30, 60]:
            if period in self._cache['ma']:
                factors[f'alpha_close_vs_ma_{period}'] = df['close'] / (self._cache['ma'][period] + 1e-8) - 1
        
        # 标准差因子 (36-45) - 使用缓存
        for period in [5, 10, 15, 20, 30, 60, 120]:
            if period in self._cache['std']:
                factors[f'alpha_std_{period}'] = self._cache['std'][period]
        
        # 最高价/最低价因子 (46-55) - 使用缓存
        for period in [5, 10, 15, 20, 30, 60]:
            if period in self._cache['max']:
                factors[f'alpha_max_{period}'] = self._cache['max'][period]
            if period in self._cache['min']:
                factors[f'alpha_min_{period}'] = self._cache['min'][period]
        
        # 价格位置因子 (56-70) - 使用缓存
        for period in [5, 10, 15, 20, 30, 60]:
            if period in self._cache['max'] and period in self._cache['min']:
                high_max = self._cache['max'][period]
                low_min = self._cache['min'][period]
                factors[f'alpha_qtlu_{period}'] = (df['close'] - low_min) / (high_max - low_min + 1e-8)
        
        # 成交量因子 (71-90) - 使用缓存
        if 'volume' in df.columns and 'volume_ma' in self._cache:
            for period in [5, 10, 15, 20, 30]:
                if period in self._cache['volume_ma']:
                    factors[f'alpha_volume_ma_{period}'] = self._cache['volume_ma'][period]
                if period in self._cache['volume_ratio']:
                    factors[f'alpha_volume_ratio_{period}'] = self._cache['volume_ratio'][period]
        
        # RSI因子 (91-100) - 使用缓存
        rsi_periods = [6, 12, 14, 21, 24, 30, 60]
        for period in rsi_periods:
            if period in self._cache['rsi']:
                factors[f'alpha_rsi_{period}'] = self._cache['rsi'][period]
        
        # EMA因子 (101-110) - 使用缓存
        ema_periods = [8, 12, 21, 26, 50, 100]
        for period in ema_periods:
            if period in self._cache['ema']:
                factors[f'alpha_ema_{period}'] = self._cache['ema'][period]
        
        # MACD系列 (111-115) - 使用缓存
        factors['alpha_macd'] = self._cache['macd']
        factors['alpha_macd_signal'] = self._cache['macd_signal']
        factors['alpha_macd_hist'] = self._cache['macd_hist']
        
        # 布林带因子 (116-125) - 使用缓存
        for period in [10, 20, 30]:
            if period in self._cache['bb_position']:
                factors[f'alpha_bb_position_{period}'] = self._cache['bb_position'][period]
        
        # 动量因子 (126-140) - 向量化计算
        momentum_periods = [1, 2, 3, 5, 10, 15, 20, 30, 60]
        for period in momentum_periods:
            factors[f'alpha_momentum_{period}'] = (df['close'] - df['close'].shift(period)) / (df['close'].shift(period) + 1e-8)
        
        # 波动率因子 (141-150) - 使用returns缓存
        returns = df['close'].pct_change()
        for period in [5, 10, 15, 20, 30, 60]:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
            factors[f'alpha_volatility_{period}'] = returns.rolling(period, min_periods=min_periods).std()
        
        # 高低价相关因子 (151-158) - 向量化计算
        factors['alpha_high_low_ratio'] = df['high'] / (df['low'] + 1e-8)
        factors['alpha_close_open_ratio'] = df['close'] / (df['open'] + 1e-8) 
        factors['alpha_high_close_ratio'] = df['high'] / (df['close'] + 1e-8)
        factors['alpha_low_close_ratio'] = df['low'] / (df['close'] + 1e-8)
        factors['alpha_open_close_ratio'] = df['open'] / (df['close'] + 1e-8)
        factors['alpha_range_close_ratio'] = (df['high'] - df['low']) / (df['close'] + 1e-8)
        factors['alpha_body_range_ratio'] = abs(df['close'] - df['open']) / (df['high'] - df['low'] + 1e-8)
        factors['alpha_upper_shadow_ratio'] = (df['high'] - np.maximum(df['close'], df['open'])) / (df['high'] - df['low'] + 1e-8)
        
        # 补充更多Alpha158因子以达到158个 - 向量化计算
        current_count = len(factors)
        print(f"  当前Alpha158因子数量: {current_count}")
        
        # 补充剩余因子
        if current_count < 158:
            remaining_needed = 158 - current_count
            print(f"  需要补充 {remaining_needed} 个因子")
            
            # 添加更多向量化因子
            # 价格分位数因子
            for period in [7, 14, 21]:
                min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(4, period//2))
                factors[f'alpha_price_rank_{period}'] = df['close'].rolling(period, min_periods=min_periods).rank() / period
            
            # 偏度和峰度因子
            for period in [5, 10, 20]:
                min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(3, period//2))
                factors[f'alpha_skewness_{period}'] = returns.rolling(period, min_periods=min_periods).skew()
                factors[f'alpha_kurtosis_{period}'] = returns.rolling(period, min_periods=min_periods).kurt()
            
            # 中位数相关因子
            for period in [9, 15, 30]:
                min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(5, period//2))
                median_val = df['close'].rolling(period, min_periods=min_periods).median()
                std_val = df['close'].rolling(period, min_periods=min_periods).std()
                factors[f'alpha_median_dev_{period}'] = (df['close'] - median_val) / (std_val + 1e-8)
            
            # 分位数范围因子
            for period in [11, 22]:
                min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(6, period//2))
                q80 = df['close'].rolling(period, min_periods=min_periods).quantile(0.8)
                q20 = df['close'].rolling(period, min_periods=min_periods).quantile(0.2)
                factors[f'alpha_quantile_range_{period}'] = q80 - q20
            
            # 价格加速度因子
            factors['alpha_price_accel_5_10'] = returns.rolling(5, min_periods=3).mean() - returns.rolling(10, min_periods=5).mean()
            factors['alpha_price_accel_10_20'] = returns.rolling(10, min_periods=5).mean() - returns.rolling(20, min_periods=10).mean()
            
            # 价格速度因子
            factors['alpha_price_velocity_5'] = (df['close'] - df['close'].shift(5)) / 5
            factors['alpha_price_velocity_10'] = (df['close'] - df['close'].shift(10)) / 10
            factors['alpha_price_velocity_20'] = (df['close'] - df['close'].shift(20)) / 20
            
            # 趋势强度因子
            for period in [5, 10, 20, 40]:
                factors[f'alpha_trend_strength_{period}'] = self._calculate_trend_strength(df['close'], period)
            
            # 波动率比率因子
            factors['alpha_vol_ratio_5_10'] = returns.rolling(5, min_periods=3).std() / (returns.rolling(10, min_periods=5).std() + 1e-8)
            factors['alpha_vol_ratio_10_20'] = returns.rolling(10, min_periods=5).std() / (returns.rolling(20, min_periods=10).std() + 1e-8)
            factors['alpha_vol_ratio_20_60'] = returns.rolling(20, min_periods=10).std() / (returns.rolling(60, min_periods=30).std() + 1e-8)
            
            # 实现波动率因子
            factors['alpha_realized_vol_5'] = np.sqrt(252) * returns.rolling(5, min_periods=3).std()
            factors['alpha_realized_vol_10'] = np.sqrt(252) * returns.rolling(10, min_periods=5).std()
            factors['alpha_realized_vol_20'] = np.sqrt(252) * returns.rolling(20, min_periods=10).std()
            
            # 高低价差因子
            if 5 in self._cache['max'] and 5 in self._cache['min']:
                factors['alpha_hl_spread_5'] = (self._cache['max'][5] - self._cache['min'][5]) / (df['close'] + 1e-8)
            if 10 in self._cache['max'] and 10 in self._cache['min']:
                factors['alpha_hl_spread_10'] = (self._cache['max'][10] - self._cache['min'][10]) / (df['close'] + 1e-8)
            if 20 in self._cache['max'] and 20 in self._cache['min']:
                factors['alpha_hl_spread_20'] = (self._cache['max'][20] - self._cache['min'][20]) / (df['close'] + 1e-8)
            
            # 技术指标因子
            factors['alpha_williams_r_14'] = self._calculate_williams_r(df, 14)
            factors['alpha_cci_14'] = self._calculate_cci(df, 14)
            factors['alpha_stoch_k_14'] = self._calculate_stoch_k(df, 14)
            factors['alpha_atr_14'] = self._calculate_atr(df, 14)
            
            # 更多MACD变种
            if 5 in self._cache['ema'] and 13 in self._cache['ema']:
                factors['alpha_macd_fast'] = self._cache['ema'][5] - self._cache['ema'][13]
            
            # 布林带相关
            if 20 in self._cache['ma'] and 20 in self._cache['std']:
                factors['alpha_bb_upper_20'] = self._cache['ma'][20] + 2 * self._cache['std'][20]
                factors['alpha_bb_lower_20'] = self._cache['ma'][20] - 2 * self._cache['std'][20]
                factors['alpha_bb_width_20'] = (2 * self._cache['std'][20]) / (self._cache['ma'][20] + 1e-8)
            
            # 价格差值因子
            for shift_period in [1, 2, 3, 5, 10]:
                factors[f'alpha_price_diff_{shift_period}'] = df['close'] - df['close'].shift(shift_period)
            
            # 价格比率因子
            for shift_period in [1, 2, 3, 5, 10]:
                factors[f'alpha_price_ratio_{shift_period}'] = df['close'] / (df['close'].shift(shift_period) + 1e-8)
            
            # 移动平均交叉因子 - 使用缓存
            if 5 in self._cache['ma'] and 10 in self._cache['ma']:
                factors['alpha_ma_cross_5_10'] = self._cache['ma'][5] / (self._cache['ma'][10] + 1e-8)
            if 10 in self._cache['ma'] and 20 in self._cache['ma']:
                factors['alpha_ma_cross_10_20'] = self._cache['ma'][10] / (self._cache['ma'][20] + 1e-8)
            if 20 in self._cache['ma'] and 60 in self._cache['ma']:
                factors['alpha_ma_cross_20_60'] = self._cache['ma'][20] / (self._cache['ma'][60] + 1e-8)
            
            # EMA交叉因子 - 使用缓存
            if 5 in self._cache['ema'] and 10 in self._cache['ema']:
                factors['alpha_ema_cross_5_10'] = self._cache['ema'][5] / (self._cache['ema'][10] + 1e-8)
            if 12 in self._cache['ema'] and 26 in self._cache['ema']:
                factors['alpha_ema_cross_12_26'] = self._cache['ema'][12] / (self._cache['ema'][26] + 1e-8)
        
        # 验证因子数量并添加性能统计
        final_count = len(factors)
        print(f"  ✅ 最终生成 {final_count} 个Alpha158因子")
        
        # 对所有因子进行未来信息泄露验证
        print("  验证因子无未来信息泄露...")
        for factor_name, factor_series in factors.items():
            FutureLeakageValidator.validate_no_future_leakage(factor_series, df.index, factor_name)
        
        # 创建DataFrame并清理
        factor_df = pd.DataFrame(factors, index=df.index)
        factor_df = factor_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return factor_df
    
    def calculate_alpha360_factors(self, df):
        """计算Alpha360扩展因子 - 向量化优化版本"""
        print("🔢 计算Alpha360扩展因子...")
        
        # 确保缓存已初始化
        self._precompute_base_series(df)
        
        factors = {}
        
        # 更多周期的基础因子 - 使用缓存
        if 120 in self._cache['ma']:
            factors['alpha360_ma_120'] = self._cache['ma'][120]
        if 240 in self._cache['max']:
            factors['alpha360_ma_240'] = self._cache['ma'][240]
        if 120 in self._cache['std']:
            factors['alpha360_std_120'] = self._cache['std'][120]  
        if 240 in self._cache['std']:
            factors['alpha360_std_240'] = self._cache['std'][240]
            
        # 更多RSI周期 - 使用缓存
        rsi_periods = [3, 9, 21, 42]
        for period in rsi_periods:
            if period in self._cache['rsi']:
                factors[f'alpha360_rsi_{period}'] = self._cache['rsi'][period]
        
        # 更多EMA组合 - 使用缓存
        ema_periods = [5, 21, 55]
        for period in ema_periods:
            if period in self._cache['ema']:
                factors[f'alpha360_ema_{period}'] = self._cache['ema'][period]
        
        # EMA差值因子 - 使用缓存
        if 5 in self._cache['ema'] and 10 in self._cache['ema']:
            factors['alpha360_ema_diff_5_10'] = self._cache['ema'][5] - self._cache['ema'][10]
        if 8 in self._cache['ema'] and 21 in self._cache['ema']:
            factors['alpha360_ema_diff_8_21'] = self._cache['ema'][8] - self._cache['ema'][21]
        if 21 in self._cache['ema'] and 55 in self._cache['ema']:
            factors['alpha360_ema_diff_21_55'] = self._cache['ema'][21] - self._cache['ema'][55]
            
            # 技术指标
        factors['alpha360_atr_14'] = self._calculate_atr(df, 14)
        factors['alpha360_atr_28'] = self._calculate_atr(df, 28)
        
        # 动量因子 - 向量化计算
        up_moves = (df['close'] > df['close'].shift(1)).astype(int)
        factors['alpha360_momentum_up_5'] = up_moves.rolling(5, min_periods=3).mean()
        factors['alpha360_momentum_up_10'] = up_moves.rolling(10, min_periods=5).mean()
        factors['alpha360_momentum_up_20'] = up_moves.rolling(20, min_periods=10).mean()
        
        # 价格分布因子 - 向量化计算
        factors['alpha360_price_quantile_20_25'] = df['close'].rolling(20, min_periods=10).quantile(0.25)
        factors['alpha360_price_quantile_20_75'] = df['close'].rolling(20, min_periods=10).quantile(0.75)
        
        # 波动率指标 - 向量化计算
        hl_ratio = np.log(df['high'] / (df['low'] + 1e-8))
        factors['alpha360_hl_volatility_10'] = hl_ratio.rolling(10, min_periods=5).std()
        factors['alpha360_hl_volatility_20'] = hl_ratio.rolling(20, min_periods=10).std()
        
        range_ratio = (df['high'] - df['low']) / (df['close'] + 1e-8)
        factors['alpha360_range_ratio_10'] = range_ratio.rolling(10, min_periods=5).mean()
        factors['alpha360_range_ratio_20'] = range_ratio.rolling(20, min_periods=10).mean()
        
        # 趋势强度 - 使用已有方法
        factors['alpha360_trend_strength_10'] = self._calculate_trend_strength(df['close'], 10)
        factors['alpha360_trend_strength_20'] = self._calculate_trend_strength(df['close'], 20)
        
        # 价格加速度 - 向量化计算
        factors['alpha360_price_accel_2'] = df['close'] - 2 * df['close'].shift(1) + df['close'].shift(2)
        factors['alpha360_price_accel_4'] = df['close'] - 2 * df['close'].shift(2) + df['close'].shift(4)
        
        # 验证因子并清理
        print(f"  ✅ 成功计算 {len(factors)} 个Alpha360因子")
        
        # 创建DataFrame并清理
        factor_df = pd.DataFrame(factors, index=df.index)
        factor_df = factor_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return factor_df
    
    def _calculate_atr(self, df, period):
        """计算ATR"""
        tr = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        return tr.rolling(period, min_periods=max(1, period//2)).mean()
    
    def _calculate_trend_strength(self, close_series, period):
        """计算趋势强度"""
        def calc_corr(x):
            if len(x) < 2:
                return 0
            return np.corrcoef(x, np.arange(len(x)))[0, 1]
        
        return close_series.rolling(period, min_periods=max(1, period//2)).apply(calc_corr)
    
    def _calculate_volume_price_trend(self, df, period):
        """计算成交量价格趋势"""
        if 'volume' not in df.columns:
            return pd.Series(0, index=df.index)
        
        price_change = df['close'].pct_change()
        volume_change = df['volume'].pct_change()
        
        # 价量同步性
        sync = (price_change * volume_change).rolling(period).mean()
        return sync.fillna(0)
    
    def _calculate_williams_r(self, df, period):
        """计算威廉指标"""
        high_max = df['high'].rolling(period).max()
        low_min = df['low'].rolling(period).min()
        
        wr = -100 * (high_max - df['close']) / (high_max - low_min + 1e-8)
        return wr.fillna(0)
    
    def _calculate_cci(self, df, period):
        """计算商品通道指标"""
        tp = (df['high'] + df['low'] + df['close']) / 3
        sma = tp.rolling(period).mean()
        mad = tp.rolling(period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        
        cci = (tp - sma) / (0.015 * mad + 1e-8)
        return cci.fillna(0)
    
    def _calculate_stoch_k(self, df, period):
        """计算随机指标K值"""
        low_min = df['low'].rolling(period).min()
        high_max = df['high'].rolling(period).max()
        
        k = 100 * (df['close'] - low_min) / (high_max - low_min + 1e-8)
        return k.fillna(50)
    
    def _calculate_linear_regression(self, series, period):
        """计算线性回归预测值"""
        def linear_reg(x):
            if len(x) < 2:
                return x.iloc[-1] if len(x) > 0 else 0
            
            y = np.array(x)
            X = np.arange(len(y))
            
            # 简单线性回归
            slope = np.cov(X, y)[0, 1] / (np.var(X) + 1e-8)
            intercept = np.mean(y) - slope * np.mean(X)
            
            # 预测下一个值
            next_x = len(y)
            prediction = slope * next_x + intercept
            
            return prediction
        
        return series.rolling(period).apply(linear_reg)
    
    def _calculate_linear_regression_slope(self, series, period):
        """计算线性回归斜率"""
        def calc_slope(x):
            if len(x) < 2:
                return 0
            
            y = np.array(x)
            X = np.arange(len(y))
            
            slope = np.cov(X, y)[0, 1] / (np.var(X) + 1e-8)
            return slope
        
        return series.rolling(period).apply(calc_slope)
    
    def _calculate_fractal_high(self, df, period):
        """计算分形高点 - 向量化优化版本"""
        high_series = df['high']
        
        # 使用rolling窗口找到局部最大值
        rolling_max = high_series.rolling(window=2*period+1, center=True, min_periods=2*period+1).max()
        fractal_high = (high_series == rolling_max).astype(int)
        
        # 边界处理：前后period个点设为0
        fractal_high.iloc[:period] = 0
        fractal_high.iloc[-period:] = 0
        
        return fractal_high
    
    def _calculate_fractal_low(self, df, period):
        """计算分形低点 - 向量化优化版本"""
        low_series = df['low']
        
        # 使用rolling窗口找到局部最小值
        rolling_min = low_series.rolling(window=2*period+1, center=True, min_periods=2*period+1).min()
        fractal_low = (low_series == rolling_min).astype(int)
        
        # 边界处理：前后period个点设为0
        fractal_low.iloc[:period] = 0
        fractal_low.iloc[-period:] = 0
        
        return fractal_low
    
    def _calculate_hammer_pattern(self, df):
        """计算锤子线形态"""
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low']
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        
        # 锤子线条件：下影线长，上影线短，实体小
        hammer = (
            (lower_shadow > 2 * body_size) &
            (upper_shadow < 0.5 * body_size) &
            (body_size < 0.3 * total_range)
        ).astype(int)
        
        return hammer
    
    def _calculate_shooting_star_pattern(self, df):
        """计算流星线形态"""
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low']
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        
        # 流星线条件：上影线长，下影线短，实体小
        shooting_star = (
            (upper_shadow > 2 * body_size) &
            (lower_shadow < 0.5 * body_size) &
            (body_size < 0.3 * total_range)
        ).astype(int)
        
        return shooting_star
    
    def calculate_all_factors(self, df):
        """计算所有因子 - 添加性能监控"""
        import time
        
        print("🔧 启动增强因子计算...")
        start_time = time.time()
        
        all_factors = {}
        
        # 1. Alpha158因子
        factor_start = time.time()
        alpha158_factors = self.calculate_alpha158_factors(df)
        all_factors.update(alpha158_factors)
        print(f"    Alpha158因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 2. Alpha360因子
        factor_start = time.time()
        alpha360_factors = self.calculate_alpha360_factors(df)
        all_factors.update(alpha360_factors)
        print(f"    Alpha360因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 3. 额外的手动因子
        factor_start = time.time()
        manual_factors = self.calculate_manual_factors(df)
        all_factors.update(manual_factors)
        print(f"    手动因子计算完成，耗时: {time.time() - factor_start:.2f}秒")
        
        # 4. 跨时间框架因子
        cross_timeframe_factors = self.calculate_cross_timeframe_factors(df)
        all_factors.update(cross_timeframe_factors)
        
        factor_df = pd.DataFrame(all_factors, index=df.index)
        total_time = time.time() - start_time
        print(f"✅ 总共计算 {len(factor_df.columns)} 个增强因子，总耗时: {total_time:.2f}秒")
        
        return factor_df
    
    def calculate_cross_timeframe_factors(self, df):
        """计算跨时间框架因子 - 向量化优化版本"""
        import time
        
        print("🔄 计算跨时间框架因子...")
        start_time = time.time()
        
        factors = {}
        
        # 定义多个时间框架
        short_periods = [3, 5, 10]       # 短期
        medium_periods = [20, 30, 60]    # 中期
        long_periods = [120, 240, 480]   # 长期
        
        # 预计算所有需要的基础动量序列，避免重复计算
        print("  预计算基础动量序列...")
        all_periods = list(set(short_periods + medium_periods + long_periods))
        momentum_cache = {}
        for period in all_periods:
            momentum_cache[period] = df['close'].pct_change(period)
        
        # 1. 多周期动量因子 - 向量化批量计算
        print("  计算多周期动量因子...")
        for short in short_periods:
            for medium in medium_periods:
                for long in long_periods:
                    # 使用缓存的动量数据
                    short_momentum = momentum_cache[short]
                    medium_momentum = momentum_cache[medium]
                    long_momentum = momentum_cache[long]
                    
                    # 向量化计算动量一致性
                    bullish_consistency = (
                        (short_momentum > 0) & 
                        (medium_momentum > 0) & 
                        (long_momentum > 0)
                    ).astype(int)
                    
                    bearish_consistency = (
                        (short_momentum < 0) & 
                        (medium_momentum < 0) & 
                        (long_momentum < 0)
                    ).astype(int)
                    
                    momentum_consistency = bullish_consistency - bearish_consistency
                    factors[f'momentum_consistency_{short}_{medium}_{long}'] = momentum_consistency
                    
                    # 动量加速度
                    momentum_acceleration = short_momentum - medium_momentum
                    factors[f'momentum_acceleration_{short}_{medium}'] = momentum_acceleration
        
                    # 验证无未来信息泄露
                    FutureLeakageValidator.validate_no_future_leakage(
                        momentum_consistency, df.index, f'momentum_consistency_{short}_{medium}_{long}'
                    )
        
        # 2. 多周期波动率因子 - 向量化优化
        print("  计算多周期波动率因子...")
        # 预计算收益率，避免重复计算
        returns = df['close'].pct_change()
        volatility_cache = {}
        for period in short_periods + medium_periods:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(1, period//2))
            volatility_cache[period] = returns.rolling(period, min_periods=min_periods).std()
        
        for short in short_periods:
            for medium in medium_periods:
                # 使用缓存的波动率数据
                short_vol = volatility_cache[short]
                medium_vol = volatility_cache[medium]
                
                # 波动率比率
                vol_ratio = short_vol / (medium_vol + 1e-8)
                factors[f'volatility_ratio_{short}_{medium}'] = vol_ratio
                
                # 波动率突破
                vol_breakout = (short_vol > medium_vol * 1.5).astype(int)
                factors[f'volatility_breakout_{short}_{medium}'] = vol_breakout
                
                # 验证无未来信息泄露
                FutureLeakageValidator.validate_no_future_leakage(
                    vol_ratio, df.index, f'volatility_ratio_{short}_{medium}'
                )
        
        # 3. 多周期趋势因子
        # print("  计算多周期趋势因子...")
        # for short in short_periods:
        #     for medium in medium_periods:
        #         # 趋势强度对比
        #         short_trend = self._calculate_trend_strength(df['close'], short)
        #         medium_trend = self._calculate_trend_strength(df['close'], medium)
                
        #         trend_strength_ratio = short_trend / (medium_trend + 1e-8)
        #         factors[f'trend_strength_ratio_{short}_{medium}'] = trend_strength_ratio
                
        #         # 趋势方向一致性
        #         short_direction = np.sign(df['close'] - df['close'].shift(short))
        #         medium_direction = np.sign(df['close'] - df['close'].shift(medium))
                
        #         trend_consistency = (short_direction == medium_direction).astype(int)
        #         factors[f'trend_consistency_{short}_{medium}'] = trend_consistency
        
        # 4. 多周期均线因子 - 向量化优化
        print("  计算多周期均线因子...")
        # 预计算所有需要的移动平均线
        ma_cache = {}
        for period in all_periods:
            min_periods = FutureLeakageValidator.validate_rolling_operations(period, max(1, period//2))
            ma_cache[period] = df['close'].rolling(period, min_periods=min_periods).mean()
        
        for short in short_periods:
            for medium in medium_periods:
                for long in long_periods:
                    # 使用缓存的移动平均数据
                    ma_short = ma_cache[short]
                    ma_medium = ma_cache[medium]
                    ma_long = ma_cache[long]
                    
                    # 向量化计算均线排列
                    bullish_alignment = (
                        (ma_short > ma_medium) & 
                        (ma_medium > ma_long)
                    ).astype(int)
                    
                    bearish_alignment = (
                        (ma_short < ma_medium) & 
                        (ma_medium < ma_long)
                    ).astype(int)
                    
                    ma_alignment = bullish_alignment - bearish_alignment
                    factors[f'ma_alignment_{short}_{medium}_{long}'] = ma_alignment
                    
                    # 验证无未来信息泄露
                    FutureLeakageValidator.validate_no_future_leakage(
                        ma_alignment, df.index, f'ma_alignment_{short}_{medium}_{long}'
                    )
        
        # 5. 多周期价格位置因子
        print("  计算多周期价格位置因子...")
        for short in short_periods:
            for medium in medium_periods:
                # 短期价格位置
                short_high = df['high'].rolling(short).max()
                short_low = df['low'].rolling(short).min()
                short_position = (df['close'] - short_low) / (short_high - short_low + 1e-8)
                
                # 中期价格位置
                medium_high = df['high'].rolling(medium).max()
                medium_low = df['low'].rolling(medium).min()
                medium_position = (df['close'] - medium_low) / (medium_high - medium_low + 1e-8)
                
                # 价格位置差异
                position_diff = short_position - medium_position
                factors[f'price_position_diff_{short}_{medium}'] = position_diff
        
        # 6. 多周期RSI因子
        print("  计算多周期RSI因子...")
        rsi_periods = [6, 14, 21, 30]
        for i, period1 in enumerate(rsi_periods):
            for period2 in rsi_periods[i+1:]:
                rsi1 = calculate_rsi(df['close'], period1)
                rsi2 = calculate_rsi(df['close'], period2)
                
                # RSI差异
                rsi_diff = rsi1 - rsi2
                factors[f'rsi_diff_{period1}_{period2}'] = rsi_diff
                
                # RSI交叉
                rsi_cross = ((rsi1 > rsi2) & (rsi1.shift(1) <= rsi2.shift(1))).astype(int) - \
                           ((rsi1 < rsi2) & (rsi1.shift(1) >= rsi2.shift(1))).astype(int)
                factors[f'rsi_cross_{period1}_{period2}'] = rsi_cross
        
        # 7. 多周期成交量因子（如果有volume数据）
        if 'volume' in df.columns:
            print("  计算多周期成交量因子...")
            for short in short_periods:
                for medium in medium_periods:
                    # 成交量比率
                    short_vol_avg = df['volume'].rolling(short).mean()
                    medium_vol_avg = df['volume'].rolling(medium).mean()
                    
                    volume_ratio = short_vol_avg / (medium_vol_avg + 1e-8)
                    factors[f'volume_ratio_{short}_{medium}'] = volume_ratio
                    
                    # 成交量突破
                    volume_breakout = (short_vol_avg > medium_vol_avg * 1.5).astype(int)
                    factors[f'volume_breakout_{short}_{medium}'] = volume_breakout
        
        # 8. 多周期价量关系因子
        if 'volume' in df.columns:
            print("  计算多周期价量关系因子...")
            for short in short_periods:
                for medium in medium_periods:
                    # 短期价量相关性
                    short_corr = df['close'].rolling(short).corr(df['volume'])
                    
                    # 中期价量相关性
                    medium_corr = df['close'].rolling(medium).corr(df['volume'])
                    
                    # 价量相关性差异
                    corr_diff = short_corr - medium_corr
                    factors[f'price_volume_corr_diff_{short}_{medium}'] = corr_diff
        
        # 9. 多周期技术指标组合
        print("  计算多周期技术指标组合...")
        for short in short_periods:
            for medium in medium_periods:
                # 多周期MACD
                macd_short = df['close'].ewm(span=short).mean() - df['close'].ewm(span=short*2).mean()
                macd_medium = df['close'].ewm(span=medium).mean() - df['close'].ewm(span=medium*2).mean()
                
                # MACD差异
                macd_diff = macd_short - macd_medium
                factors[f'macd_diff_{short}_{medium}'] = macd_diff
                
                # MACD交叉
                macd_cross = ((macd_short > macd_medium) & (macd_short.shift(1) <= macd_medium.shift(1))).astype(int) - \
                            ((macd_short < macd_medium) & (macd_short.shift(1) >= macd_medium.shift(1))).astype(int)
                factors[f'macd_cross_{short}_{medium}'] = macd_cross
        
        # 10. 多周期支撑阻力因子
        print("  计算多周期支撑阻力因子...")
        for short in short_periods:
            for medium in medium_periods:
                # 短期支撑阻力
                short_support = df['low'].rolling(short).min()
                short_resistance = df['high'].rolling(short).max()
                
                # 中期支撑阻力
                medium_support = df['low'].rolling(medium).min()
                medium_resistance = df['high'].rolling(medium).max()
                
                # 支撑阻力强度
                support_strength = (df['close'] - short_support) / (medium_support - short_support + 1e-8)
                resistance_strength = (short_resistance - df['close']) / (short_resistance - medium_resistance + 1e-8)
                
                factors[f'support_strength_{short}_{medium}'] = support_strength
                factors[f'resistance_strength_{short}_{medium}'] = resistance_strength
        
        # 添加性能统计
        total_time = time.time() - start_time
        print(f"  ✅ 生成 {len(factors)} 个跨时间框架因子，耗时: {total_time:.2f}秒")
        
        return factors
    
    def calculate_manual_factors(self, df):
        """计算额外的手动因子"""
        factors = {}
        
        # K线形态因子
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low'] + 1e-8
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        
        factors['body_ratio'] = body_size / total_range
        factors['upper_shadow_ratio'] = upper_shadow / total_range
        factors['lower_shadow_ratio'] = lower_shadow / total_range
        
        # 缺口因子
        factors['gap_up'] = (df['open'] > df['high'].shift(1)).astype(int)
        factors['gap_down'] = (df['open'] < df['low'].shift(1)).astype(int)
        factors['gap_size'] = (df['open'] - df['close'].shift(1)) / (df['close'].shift(1) + 1e-8)
        
        # 成交量因子（如果有成交量数据）
        if 'volume' in df.columns:
            factors['volume_change'] = df['volume'].pct_change()
            factors['price_volume'] = df['close'] * df['volume']
            
            # OBV指标
            price_change = df['close'].diff()
            volume_direction = np.where(price_change > 0, df['volume'], 
                                      np.where(price_change < 0, -df['volume'], 0))
            obv = pd.Series(volume_direction, index=df.index).cumsum()
            factors['obv'] = obv
            factors['obv_ma_10'] = obv.rolling(10, min_periods=5).mean()
        
        return factors


class FactorCombinationOptimizer:
    """因子组合优化器"""
    def __init__(self, max_interaction_order=2, correlation_threshold=0.8):
        self.max_interaction_order = max_interaction_order
        self.correlation_threshold = correlation_threshold
        
    def generate_factor_interactions(self, factor_df, top_factors=None, max_combinations=50):
        """生成因子交互项"""
        print("🔄 生成因子交互项...")
        
        if top_factors is None:
            top_factors = factor_df.columns.tolist()
        else:
            # 确保top_factors在factor_df中存在
            top_factors = [f for f in top_factors if f in factor_df.columns]
        
        if len(top_factors) > 20:
            print(f"  因子数量过多({len(top_factors)})，随机选择20个进行交互")
            np.random.seed(42)
            top_factors = np.random.choice(top_factors, 20, replace=False).tolist()
        
        interaction_factors = {}
        
        # 1. 二阶交互项（乘积）
        print("  生成二阶乘积交互项...")
        combination_count = 0
        for i, factor1 in enumerate(top_factors):
            for j, factor2 in enumerate(top_factors[i+1:], i+1):
                if combination_count >= max_combinations:
                    break
                
                # 乘积交互
                interaction_name = f'interact_mult_{factor1}_{factor2}'
                interaction_factors[interaction_name] = factor_df[factor1] * factor_df[factor2]
                
                combination_count += 1
            
            if combination_count >= max_combinations:
                break
        
        # 2. 比值交互项
        print("  生成比值交互项...")
        combination_count = 0
        for i, factor1 in enumerate(top_factors):
            for j, factor2 in enumerate(top_factors[i+1:], i+1):
                if combination_count >= max_combinations:
                    break
                
                # 比值交互
                interaction_name = f'interact_ratio_{factor1}_{factor2}'
                interaction_factors[interaction_name] = factor_df[factor1] / (factor_df[factor2] + 1e-8)
                
                combination_count += 1
            
            if combination_count >= max_combinations:
                break
        
        # 3. 非线性交互项
        print("  生成非线性交互项...")
        combination_count = 0
        for i, factor1 in enumerate(top_factors[:10]):  # 限制数量
            if combination_count >= max_combinations // 2:
                break
            
            # 平方项
            square_name = f'interact_square_{factor1}'
            interaction_factors[square_name] = factor_df[factor1] ** 2
            
            # 立方根项
            cbrt_name = f'interact_cbrt_{factor1}'
            interaction_factors[cbrt_name] = np.sign(factor_df[factor1]) * np.abs(factor_df[factor1]) ** (1/3)
            
            combination_count += 2
        
        # 清理交互项
        interaction_factors = self._clean_interaction_factors(interaction_factors)
        
        print(f"  ✅ 生成 {len(interaction_factors)} 个因子交互项")
        return pd.DataFrame(interaction_factors, index=factor_df.index)
    
    def _clean_interaction_factors(self, factors_dict):
        """清理交互项因子"""
        cleaned_factors = {}
        
        for name, factor_series in factors_dict.items():
            # 替换无穷大值
            factor_series = factor_series.replace([np.inf, -np.inf], np.nan)
            
            # 检查是否全为NaN
            if not factor_series.isna().all():
                # 异常值处理
                q1 = factor_series.quantile(0.01)
                q99 = factor_series.quantile(0.99)
                factor_series = factor_series.clip(q1, q99)
                
                # 标准化
                factor_series = (factor_series - factor_series.mean()) / (factor_series.std() + 1e-8)
                
                cleaned_factors[name] = factor_series
        
        return cleaned_factors
    
    def optimize_factor_combination(self, factor_df, target_series, method='correlation', top_n=50):
        """优化因子组合"""
        print(f"🎯 优化因子组合 (方法: {method})...")
        
        # 数据对齐
        common_index = factor_df.index.intersection(target_series.index)
        factor_aligned = factor_df.loc[common_index]
        target_aligned = target_series.loc[common_index]
        
        # 去除NaN
        mask = ~target_aligned.isna()
        factor_clean = factor_aligned[mask]
        target_clean = target_aligned[mask]
        
        if len(factor_clean) < 100:
            print("  警告: 有效数据不足")
            return factor_df.columns.tolist()[:top_n]
        
        if method == 'correlation':
            return self._optimize_by_correlation(factor_clean, target_clean, top_n)
        elif method == 'lasso':
            return self._optimize_by_lasso(factor_clean, target_clean, top_n)
        else:
            print(f"  警告: 未知的优化方法 {method}，使用相关性方法")
            return self._optimize_by_correlation(factor_clean, target_clean, top_n)
    
    def _optimize_by_correlation(self, factor_df, target_series, top_n):
        """基于相关性的因子选择"""
        correlations = {}
        
        for col in factor_df.columns:
            try:
                corr = factor_df[col].corr(target_series)
                if not pd.isna(corr):
                    correlations[col] = abs(corr)
            except:
                continue
        
        # 按相关性排序
        sorted_factors = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        
        # 去除高相关性因子
        selected_factors = []
        for factor_name, corr in sorted_factors:
            if len(selected_factors) >= top_n:
                break
            
            # 检查与已选因子的相关性
            is_redundant = False
            for selected_factor in selected_factors:
                factor_corr = factor_df[factor_name].corr(factor_df[selected_factor])
                if abs(factor_corr) > self.correlation_threshold:
                    is_redundant = True
                    break
            
            if not is_redundant:
                selected_factors.append(factor_name)
        
        print(f"  ✅ 基于相关性选择 {len(selected_factors)} 个因子")
        return selected_factors
    
    def _optimize_by_lasso(self, factor_df, target_series, top_n):
        """基于Lasso回归的因子选择"""
        try:
            from sklearn.linear_model import LassoCV
            from sklearn.preprocessing import StandardScaler
            
            # 标准化
            scaler = StandardScaler()
            factor_scaled = scaler.fit_transform(factor_df.values)
            
            # Lasso回归
            lasso = LassoCV(cv=5, random_state=42, max_iter=1000)
            lasso.fit(factor_scaled, target_series.values)
            
            # 选择非零系数的因子
            selected_indices = np.where(lasso.coef_ != 0)[0]
            
            if len(selected_indices) == 0:
                print("  警告: Lasso未选择任何因子，回退到相关性方法")
                return self._optimize_by_correlation(factor_df, target_series, top_n)
            
            # 按系数绝对值排序
            factor_importance = [(factor_df.columns[i], abs(lasso.coef_[i])) 
                               for i in selected_indices]
            factor_importance.sort(key=lambda x: x[1], reverse=True)
            
            selected_factors = [factor for factor, importance in factor_importance[:top_n]]
            
            print(f"  ✅ 基于Lasso选择 {len(selected_factors)} 个因子")
            return selected_factors
            
        except ImportError:
            print("  警告: sklearn不可用，回退到相关性方法")
            return self._optimize_by_correlation(factor_df, target_series, top_n)


class StableGRU(nn.Module):
    """增强版数值稳定的GRU回归模型 - 大幅提升复杂度"""
    
    def __init__(self, input_size, hidden_size=256, num_layers=3, dropout=0.15, num_targets=6):
        super(StableGRU, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # 输入处理层 - 增强版
        self.input_norm = nn.LayerNorm(input_size)
        self.input_projection = nn.Linear(input_size, hidden_size)
        
        # 多层双向GRU - 大幅增强建模能力
        self.gru = nn.GRU(
            input_size=hidden_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True,
            bidirectional=True  # 双向GRU增强建模能力
        )
        
        # 多头注意力机制 - 新增
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,  # 双向GRU
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 特征融合层 - 新增
        self.feature_fusion = nn.Sequential(
            nn.LayerNorm(hidden_size * 2),
            nn.Linear(hidden_size * 2, hidden_size),
            nn.GELU(),  # 使用GELU激活函数
            nn.Dropout(dropout)
        )
        
        # 增强的输出层 - 更深更复杂
        self.output_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LayerNorm(hidden_size // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.7),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.LayerNorm(hidden_size // 4),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_size // 4, num_targets)
        )
        
        # 残差连接 - 新增
        self.residual_projection = nn.Linear(input_size, num_targets)
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """优化权重初始化"""
        for name, param in self.named_parameters():
            if 'weight' in name and len(param.shape) >= 2:
                nn.init.xavier_normal_(param, gain=0.02)  # 使用xavier_normal
            elif 'bias' in name:
                nn.init.constant_(param, 0)
        
    def forward(self, x):
        batch_size, seq_len, input_size = x.shape
        
        # 输入处理
        x_norm = self.input_norm(x)
        x_proj = self.input_projection(x_norm)
        
        # 双向GRU处理
        gru_out, _ = self.gru(x_proj)
        
        # 多头注意力机制
        attn_out, attn_weights = self.attention(gru_out, gru_out, gru_out)
        
        # 取最后时间步
        last_output = attn_out[:, -1, :]
        
        # 特征融合
        fused_features = self.feature_fusion(last_output)
        
        # 主要输出
        main_output = self.output_layers(fused_features)
        
        # 残差连接
        residual = self.residual_projection(x_norm.mean(dim=1))  # 全局平均池化
        
        # 组合输出
        output = main_output + 0.1 * residual
        
        return output

# 保持向后兼容
SimpleGRU = StableGRU


class TradingOptimizedLoss(nn.Module):
    """交易导向的损失函数 - 专门优化交易信号质量"""
    
    def __init__(self, transaction_cost=0.002, signal_weight=0.6, return_weight=0.4, pos_weight=None):
        super().__init__()
        self.transaction_cost = transaction_cost
        self.signal_weight = signal_weight
        self.return_weight = return_weight
        
        # 信号质量损失（关键）
        if pos_weight is not None:
            self.signal_loss = nn.BCEWithLogitsLoss(pos_weight=torch.tensor(pos_weight))
        else:
            self.signal_loss = nn.BCEWithLogitsLoss()
        
        # 收益率预测损失
        self.return_loss = nn.MSELoss()
        
    def forward(self, pred, target):
        """
        pred: [batch_size, 2] - [收益率预测, 方向预测logits]
        target: [batch_size, 2] - [收益率目标, 方向目标]
        """
        pred_return = pred[:, 0]      # 收益率预测
        pred_signal = pred[:, 1]      # 信号预测(logits)
        
        target_return = target[:, 0]      # 收益率目标
        target_signal = target[:, 1]     # 信号目标(0或1)
        
        # 1. 信号质量损失（最重要）
        # 重点优化：能否正确识别超过阈值的机会
        signal_loss = self.signal_loss(pred_signal, target_signal)
        
        # 2. 收益率预测损失（辅助）
        return_loss = self.return_loss(pred_return, target_return)
        
        # 3. 交易导向的加权损失
        # 对于预测为"看多"的样本，更重视收益率准确性
        # pred_probs = torch.sigmoid(pred_signal)
        # buy_mask = pred_probs > 0.5  # 预测看多的样本
        
        # if buy_mask.sum() > 0:
        #     # 对看多信号的收益率预测给予额外权重
        #     buy_return_loss = self.return_loss(
        #         pred_return[buy_mask], 
        #         target_return[buy_mask]
        #     )
        #     # 如果预测看多但实际亏损，给予额外惩罚
        #     buy_penalty = torch.mean(
        #         torch.clamp(self.transaction_cost - target_return[buy_mask], min=0) ** 2
        #     )
        #     signal_loss = signal_loss + 0.5 * buy_return_loss + 0.3 * buy_penalty
        
        # 最终损失
        total_loss = (self.signal_weight * signal_loss + 
                     self.return_weight * return_loss)
        
        return total_loss


class ProfitFocusedLoss(nn.Module):
    """利润导向损失函数 - 直接优化交易盈利能力"""
    
    def __init__(self, transaction_cost=0.002, profit_weight=0.7, accuracy_weight=0.3):
        super().__init__()
        self.transaction_cost = transaction_cost
        self.profit_weight = profit_weight
        self.accuracy_weight = accuracy_weight
        self.bce_loss = nn.BCEWithLogitsLoss()
        
    def forward(self, pred, target):
        pred_return = pred[:, 0]      # 收益率预测
        pred_signal = pred[:, 1]      # 信号预测(logits)
        
        target_return = target[:, 0]      # 实际收益率
        target_signal = target[:, 1]     # 信号目标
        
        # 1. 基础分类损失
        classification_loss = self.bce_loss(pred_signal, target_signal)
        
        # # 2. 利润导向损失（核心创新）
        # pred_probs = torch.sigmoid(pred_signal)
        
        # # 计算每个样本的"交易决策损失"
        # profit_loss = torch.zeros_like(pred_probs)
        
        # # 对于预测看多的样本（pred_probs > 0.5）
        # buy_mask = pred_probs > 0.5
        # if buy_mask.sum() > 0:
        #     # 如果预测看多但实际收益 < 交易成本，给予惩罚
        #     actual_profit = target_return[buy_mask]
        #     profit_penalty = torch.clamp(self.transaction_cost - actual_profit, min=0) ** 2
        #     profit_loss[buy_mask] = profit_penalty
            
        # # 对于预测看空的样本（pred_probs <= 0.5）
        # sell_mask = pred_probs <= 0.5  
        # if sell_mask.sum() > 0:
        #     # 如果预测看空但实际收益 >= 交易成本，给予惩罚（错失机会）
        #     actual_profit = target_return[sell_mask]
        #     opportunity_penalty = torch.clamp(actual_profit - self.transaction_cost, min=0) ** 2
        #     profit_loss[sell_mask] = opportunity_penalty * 0.5  # 错失机会的惩罚相对较轻
        
        # profit_loss = torch.mean(profit_loss)
        
        # 最终损失
        # total_loss = (self.accuracy_weight * classification_loss + 
        #              self.profit_weight * profit_loss)
        total_loss = classification_loss
        return total_loss


# 保持向后兼容
DirectionOptimizedLoss = TradingOptimizedLoss


class StateAdaptiveNetwork(nn.Module):
    """状态自适应神经网络"""
    
    def __init__(self, input_size, hidden_size=64, num_heads=4, num_layers=2, 
                 dropout=0.1, state_feature_size=13):
        super(StateAdaptiveNetwork, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.state_feature_size = state_feature_size
        
        # 因子特征编码器 - GRU
        self.factor_encoder = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 市场状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_feature_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU()
        )
        
        # 多头注意力机制
        self.multihead_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 状态自适应门控网络
        self.state_gate = nn.Sequential(
            nn.Linear(hidden_size + hidden_size // 4, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size),
            nn.Sigmoid()  # 门控值在0-1之间
        )
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU(),
            nn.Linear(hidden_size // 4, 1)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if len(param.shape) >= 2:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.uniform_(param, -0.1, 0.1)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def forward(self, factor_sequence, market_state_features):
        """前向传播"""
        batch_size = factor_sequence.size(0)
        
        # 1. 因子序列编码
        factor_encoded, hidden_state = self.factor_encoder(factor_sequence)
        # factor_encoded: (batch_size, sequence_length, hidden_size)
        
        # 2. 多头注意力机制
        attended_features, attention_weights = self.multihead_attention(
            factor_encoded, factor_encoded, factor_encoded
        )
        # attended_features: (batch_size, sequence_length, hidden_size)
        
        # 3. 取最后一个时间步的特征
        last_step_features = attended_features[:, -1, :]  # (batch_size, hidden_size)
        
        # 4. 市场状态编码
        state_encoded = self.state_encoder(market_state_features)
        # state_encoded: (batch_size, hidden_size // 4)
        
        # 5. 状态自适应门控
        combined_features = torch.cat([last_step_features, state_encoded], dim=1)
        # combined_features: (batch_size, hidden_size + hidden_size // 4)
        
        gate_weights = self.state_gate(combined_features)
        # gate_weights: (batch_size, hidden_size)
        
        # 6. 应用门控机制
        gated_features = last_step_features * gate_weights
        # gated_features: (batch_size, hidden_size)
        
        # 7. 特征融合
        fused_features = self.feature_fusion(gated_features)
        # fused_features: (batch_size, hidden_size)
        
        # 8. 预测输出
        prediction = self.prediction_head(fused_features)
        # prediction: (batch_size, 1)
        
        return prediction
    
    def get_attention_weights(self, factor_sequence, market_state_features):
        """获取注意力权重（用于可解释性分析）"""
        with torch.no_grad():
            # 因子序列编码
            factor_encoded, _ = self.factor_encoder(factor_sequence)
            
            # 多头注意力
            _, attention_weights = self.multihead_attention(
                factor_encoded, factor_encoded, factor_encoded
            )
            
            return attention_weights
    
    def get_gate_weights(self, factor_sequence, market_state_features):
        """获取门控权重（用于分析状态自适应机制）"""
        with torch.no_grad():
            # 因子序列编码
            factor_encoded, _ = self.factor_encoder(factor_sequence)
            
            # 多头注意力
            attended_features, _ = self.multihead_attention(
                factor_encoded, factor_encoded, factor_encoded
            )
            
            # 取最后一个时间步
            last_step_features = attended_features[:, -1, :]
            
            # 市场状态编码
            state_encoded = self.state_encoder(market_state_features)
            
            # 门控权重
            combined_features = torch.cat([last_step_features, state_encoded], dim=1)
            gate_weights = self.state_gate(combined_features)
            
            return gate_weights


class ReturnPredictionEvaluator:
    """收益率预测评估器"""
    
    def __init__(self, transaction_cost=0.0005):
        self.transaction_cost = transaction_cost  # 交易成本（0.05%）
        
    def evaluate_return_predictions(self, predictions, actuals, target_names, timestamps=None):
        """评估收益率预测结果"""
        print("🎯 收益率预测专业评估")
        print("="*60)
        
        evaluation_results = {}
        
        # 只处理收益率预测，忽略方向分类
        return_targets = [name for name in target_names if 'return' in name]
        
        print(f"📊 评估 {len(return_targets)} 个收益率预测目标:")
        print()
        
        for i, target_name in enumerate(target_names):
            if 'return' not in target_name:
                continue  # 跳过非收益率目标
            
            pred_returns = predictions[:, i]
            actual_returns = actuals[:, i]
            
            # 基础统计指标
            basic_metrics = self._calculate_basic_metrics(pred_returns, actual_returns)
            
            # 方向准确性（修复版）
            direction_metrics = self._calculate_direction_accuracy(pred_returns, actual_returns)
            
            # 胜率和盈亏比
            trading_metrics = self._calculate_trading_metrics(pred_returns, actual_returns)
            
            # 分档收益分析
            quantile_analysis = self._analyze_quantile_performance(pred_returns, actual_returns)
            
            # 汇总结果
            target_results = {
                'basic_metrics': basic_metrics,
                'direction_metrics': direction_metrics,
                'trading_metrics': trading_metrics,
                'quantile_analysis': quantile_analysis
            }
            
            evaluation_results[target_name] = target_results
            
            # 输出结果
            self._print_target_results(target_name, target_results)
        
        # 整体评估摘要
        self._print_overall_summary(evaluation_results)
        
        return evaluation_results
    
    def _calculate_basic_metrics(self, pred_returns, actual_returns):
        """计算基础统计指标"""
        mse = np.mean((pred_returns - actual_returns) ** 2)
        mae = np.mean(np.abs(pred_returns - actual_returns))
        rmse = np.sqrt(mse)
        
        # 相关性
        corr = np.corrcoef(pred_returns, actual_returns)[0, 1]
        if np.isnan(corr):
            corr = 0.0
        
        # R²
        ss_res = np.sum((actual_returns - pred_returns) ** 2)
        ss_tot = np.sum((actual_returns - np.mean(actual_returns)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        return {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'correlation': corr,
            'r_squared': r_squared
        }
    
    def _calculate_direction_accuracy(self, pred_returns, actual_returns):
        """计算方向准确性（修复版）"""
        
        # 🔧 修复：统一使用 >= 0 的二分类逻辑
        pred_direction = (pred_returns >= 0).astype(int)  # 0=下跌, 1=上涨
        actual_direction = (actual_returns >= 0).astype(int)  # 0=下跌, 1=上涨
        
        # 基础方向准确性
        direction_accuracy = np.mean(pred_direction == actual_direction)
        
        # 考虑交易成本的方向准确性
        pred_direction_cost = (pred_returns >= self.transaction_cost).astype(int)
        actual_direction_cost = (actual_returns >= self.transaction_cost).astype(int)
        direction_accuracy_with_cost = np.mean(pred_direction_cost == actual_direction_cost)
        
        # 上涨下跌的准确性分别计算
        up_mask = actual_direction == 1
        down_mask = actual_direction == 0
        
        up_accuracy = np.mean(pred_direction[up_mask] == actual_direction[up_mask]) if up_mask.sum() > 0 else 0
        down_accuracy = np.mean(pred_direction[down_mask] == actual_direction[down_mask]) if down_mask.sum() > 0 else 0
        
        return {
            'direction_accuracy': direction_accuracy,
            'direction_accuracy_with_cost': direction_accuracy_with_cost,
            'up_accuracy': up_accuracy,
            'down_accuracy': down_accuracy,
            'up_samples': up_mask.sum(),
            'down_samples': down_mask.sum()
        }
    
    def _calculate_trading_metrics(self, pred_returns, actual_returns):
        """计算交易指标：胜率和盈亏比"""
        
        # 🔧 基于预测信号的交易策略 - 修复版
        # 策略1：预测收益率为正就买入（更合理的策略）
        buy_signals = pred_returns > 0  # 预测收益率为正
        
        if buy_signals.sum() == 0:
            return {
                'strategy1_win_rate': 0,
                'strategy1_profit_loss_ratio': 0,
                'strategy1_total_trades': 0,
                'strategy1_total_return': 0,
                'strategy2_win_rate': 0,
                'strategy2_profit_loss_ratio': 0,
                'strategy2_total_trades': 0,
                'strategy2_total_return': 0
            }
        
        # 策略1：预测上涨 > 阈值就买入
        strategy1_returns = actual_returns[buy_signals]
        strategy1_profits = strategy1_returns[strategy1_returns > 0]
        strategy1_losses = strategy1_returns[strategy1_returns <= 0]
        
        strategy1_win_rate = len(strategy1_profits) / len(strategy1_returns) if len(strategy1_returns) > 0 else 0
        strategy1_avg_profit = np.mean(strategy1_profits) if len(strategy1_profits) > 0 else 0
        strategy1_avg_loss = np.mean(np.abs(strategy1_losses)) if len(strategy1_losses) > 0 else 0
        strategy1_profit_loss_ratio = strategy1_avg_profit / strategy1_avg_loss if strategy1_avg_loss > 0 else 0
        strategy1_total_return = np.sum(strategy1_returns)
        
        # 策略2：预测下跌就卖空
        sell_signals = pred_returns < 0  # 预测收益率为负
        
        if sell_signals.sum() > 0:
            # 卖空策略：实际收益率为负时盈利
            strategy2_returns = -actual_returns[sell_signals]  # 卖空收益
            strategy2_profits = strategy2_returns[strategy2_returns > 0]
            strategy2_losses = strategy2_returns[strategy2_returns <= 0]
            
            strategy2_win_rate = len(strategy2_profits) / len(strategy2_returns) if len(strategy2_returns) > 0 else 0
            strategy2_avg_profit = np.mean(strategy2_profits) if len(strategy2_profits) > 0 else 0
            strategy2_avg_loss = np.mean(np.abs(strategy2_losses)) if len(strategy2_losses) > 0 else 0
            strategy2_profit_loss_ratio = strategy2_avg_profit / strategy2_avg_loss if strategy2_avg_loss > 0 else 0
            strategy2_total_return = np.sum(strategy2_returns)
        else:
            strategy2_win_rate = 0
            strategy2_profit_loss_ratio = 0
            strategy2_total_return = 0
        
        return {
            'strategy1_win_rate': strategy1_win_rate,
            'strategy1_profit_loss_ratio': strategy1_profit_loss_ratio,
            'strategy1_total_trades': len(strategy1_returns),
            'strategy1_total_return': strategy1_total_return,
            'strategy2_win_rate': strategy2_win_rate,
            'strategy2_profit_loss_ratio': strategy2_profit_loss_ratio,
            'strategy2_total_trades': sell_signals.sum(),
            'strategy2_total_return': strategy2_total_return
        }
    
    def _analyze_quantile_performance(self, pred_returns, actual_returns):
        """分档收益分析"""
        
        # 按预测值分成5档
        try:
            pred_quantiles = pd.qcut(pred_returns, q=5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'])
            quantile_analysis = {}
            
            for quantile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
                mask = pred_quantiles == quantile
                if mask.sum() > 0:
                    quantile_actual = actual_returns[mask]
                    quantile_analysis[quantile] = {
                        'count': mask.sum(),
                        'avg_actual_return': np.mean(quantile_actual),
                        'win_rate': np.mean(quantile_actual > 0),
                        'std_actual_return': np.std(quantile_actual)
                    }
                else:
                    quantile_analysis[quantile] = {
                        'count': 0,
                        'avg_actual_return': 0,
                        'win_rate': 0,
                        'std_actual_return': 0
                    }
            
            return quantile_analysis
        except Exception as e:
            print(f"    分档分析失败: {e}")
            return {}
    
    def _print_target_results(self, target_name, results):
        """输出单个目标的结果"""
        print(f"📈 {target_name} 详细评估:")
        print("-" * 50)
        
        # 基础指标
        basic = results['basic_metrics']
        print(f"  基础指标:")
        print(f"    MSE: {basic['mse']:.6f}")
        print(f"    MAE: {basic['mae']:.6f}")
        print(f"    RMSE: {basic['rmse']:.6f}")
        print(f"    相关性: {basic['correlation']:.4f}")
        print(f"    R²: {basic['r_squared']:.4f}")
        
        # 方向准确性
        direction = results['direction_metrics']
        print(f"  方向准确性:")
        print(f"    基础方向准确性: {direction['direction_accuracy']:.2%}")
        print(f"    考虑交易成本: {direction['direction_accuracy_with_cost']:.2%}")
        print(f"    上涨准确性: {direction['up_accuracy']:.2%} ({direction['up_samples']}样本)")
        print(f"    下跌准确性: {direction['down_accuracy']:.2%} ({direction['down_samples']}样本)")
        
        # 交易指标
        trading = results['trading_metrics']
        print(f"  交易策略指标:")
        print(f"    做多策略:")
        print(f"      胜率: {trading['strategy1_win_rate']:.2%}")
        print(f"      盈亏比: {trading['strategy1_profit_loss_ratio']:.2f}")
        print(f"      交易次数: {trading['strategy1_total_trades']}")
        print(f"      总收益: {trading['strategy1_total_return']:.2f}%")
        
        if trading['strategy2_total_trades'] > 0:
            print(f"    做空策略:")
            print(f"      胜率: {trading['strategy2_win_rate']:.2%}")
            print(f"      盈亏比: {trading['strategy2_profit_loss_ratio']:.2f}")
            print(f"      交易次数: {trading['strategy2_total_trades']}")
            print(f"      总收益: {trading['strategy2_total_return']:.2f}%")
        
        # 分档分析
        quantile = results['quantile_analysis']
        if quantile:
            print(f"  分档收益分析:")
            for q in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
                if q in quantile:
                    data = quantile[q]
                    print(f"    {q}: 平均收益{data['avg_actual_return']:.3f}%, "
                          f"胜率{data['win_rate']:.1%}, "
                          f"样本{data['count']}")
        
        print()
    
    def _print_overall_summary(self, evaluation_results):
        """输出整体评估摘要"""
        print("🎯 整体评估摘要")
        print("="*60)
        
        # 计算平均指标
        correlations = []
        direction_accuracies = []
        win_rates = []
        profit_loss_ratios = []
        
        for target_name, results in evaluation_results.items():
            correlations.append(results['basic_metrics']['correlation'])
            direction_accuracies.append(results['direction_metrics']['direction_accuracy'])
            win_rates.append(results['trading_metrics']['strategy1_win_rate'])
            profit_loss_ratios.append(results['trading_metrics']['strategy1_profit_loss_ratio'])
        
        print(f"平均相关性: {np.mean(correlations):.4f}")
        print(f"平均方向准确性: {np.mean(direction_accuracies):.2%}")
        print(f"平均胜率: {np.mean(win_rates):.2%}")
        print(f"平均盈亏比: {np.mean(profit_loss_ratios):.2f}")
        
        # 建议
        print(f"\n💡 交易建议:")
        best_target = max(evaluation_results.keys(), 
                         key=lambda x: evaluation_results[x]['trading_metrics']['strategy1_win_rate'])
        print(f"  最佳预测目标: {best_target}")
        print(f"  胜率: {evaluation_results[best_target]['trading_metrics']['strategy1_win_rate']:.2%}")
        print(f"  盈亏比: {evaluation_results[best_target]['trading_metrics']['strategy1_profit_loss_ratio']:.2f}")
        
        if np.mean(win_rates) > 0.55 and np.mean(profit_loss_ratios) > 1.0:
            print("  ✅ 策略可能有效，建议进一步验证")
        else:
            print("  ⚠️ 策略效果一般，建议优化模型或调整参数")


def create_simplified_return_labels(df, periods=[30, 60, 120]):
    """创建简化的收益率标签 - 只预测收益率，不做分类"""
    print("📊 创建简化的收益率标签...")
    
    labels = {}
    
    for period in periods:
        # 🔧 修复：只创建收益率标签
        future_return = df['close'].shift(-period) / df['close'] - 1
        labels[f'return_{period}min'] = future_return * 100  # 转换为百分比
        
        # 验证数据质量
        valid_return = future_return.dropna()
        if len(valid_return) > 0:
            pos_count = np.sum(valid_return > 0)
            neg_count = np.sum(valid_return < 0)
            zero_count = np.sum(valid_return == 0)
            total = len(valid_return)
            
            print(f"  {period}分钟收益率:")
            print(f"    正收益: {pos_count} ({pos_count/total:.1%})")
            print(f"    负收益: {neg_count} ({neg_count/total:.1%})")
            print(f"    零收益: {zero_count} ({zero_count/total:.1%})")
            print(f"    平均收益: {valid_return.mean()*100:.4f}%")
            print(f"    收益波动: {valid_return.std()*100:.4f}%")
    
    return pd.DataFrame(labels, index=df.index)


def verify_return_calculation(df, periods=[30, 60, 120]):
    """验证收益率计算的正确性"""
    print("🔍 验证收益率计算正确性")
    print("="*60)
    
    # 选择几个测试时间点
    test_indices = [1000, 2000, 3000] if len(df) > 3500 else [500, 1000, 1500]
    
    for idx in test_indices:
        if idx + max(periods) >= len(df):
            continue
            
        current_time = df.index[idx]
        current_price = df['close'].iloc[idx]
        
        print(f"\n验证时间点: {current_time}")
        print(f"当前价格: {current_price:.6f}")
        
        for period in periods:
            # 手动计算
            future_time = df.index[idx + period]
            future_price = df['close'].iloc[idx + period]
            manual_return = (future_price / current_price - 1) * 100
            
            # 使用shift计算
            shift_return = ((df['close'].shift(-period) / df['close'] - 1) * 100).iloc[idx]
            
            print(f"  {period}分钟后 ({future_time}):")
            print(f"    未来价格: {future_price:.6f}")
            print(f"    手动计算: {manual_return:.4f}%")
            print(f"    shift计算: {shift_return:.4f}%")
            print(f"    差异: {abs(manual_return - shift_return):.8f}%")
            print(f"    一致性: {'✅' if abs(manual_return - shift_return) < 1e-6 else '❌'}")
    
    print("\n📋 收益率计算验证完成")


# 修改主训练管道，使用新的评估器
def improved_training_pipeline(df):
    """改进的训练管道 - 专注于收益率预测，跳过因子筛选"""
    print("🚀 启动改进的收益率预测训练管道")
    print("="*70)
    
    # 1. 验证收益率计算
    print("第一步: 验证收益率计算正确性")
    verify_return_calculation(df)
    
    # 2. 数据质量管理
    print("\n第二步: 数据质量管理")
    dm = DataQualityManager(nan_threshold=0.3, min_valid_samples=1000)
    dm.diagnose_data_quality(df[['open', 'high', 'low', 'close', 'volume']])
    
    # 创建稳健因子
    robust_factors = dm.create_robust_factors(df)
    clean_factors = dm.advanced_nan_handling(robust_factors, method='smart_fill')
    
    print(f"✅ 数据质量管理完成: {len(clean_factors)} 行 × {len(clean_factors.columns)} 列")
    
    # 3. 创建简化的收益率标签
    print("\n第三步: 创建收益率标签")
    # target_periods = [30, 60, 120]
    target_periods = [120]
    labels_df = create_simplified_return_labels(df, periods=target_periods)
    
    # 4. 数据对齐
    print("\n第四步: 数据对齐")
    common_index = clean_factors.index.intersection(labels_df.index)
    clean_factors = clean_factors.loc[common_index]
    labels_df = labels_df.loc[common_index]
    
    # 合并并清理
    combined_df = pd.concat([clean_factors, labels_df], axis=1)
    combined_df = combined_df.dropna()
    
    print(f"对齐后数据集: {len(combined_df)} 行 × {len(combined_df.columns)} 列")
    
    if len(combined_df) < 500:
        print("❌ 数据量不足")
        return None
    
    # 🔧 更新：跳过因子质量评估，直接使用所有可用因子
    print("\n第五步: 因子选择")
    print("  - 跳过因子质量评估，使用所有生成的因子。")
    
    # 提取所有因子列名
    selected_factor_names = clean_factors.columns.tolist()
    
    print(f"✅ 使用全部 {len(selected_factor_names)} 个因子进行训练:")
    # 打印前10个因子作为示例
    for i, factor_name in enumerate(selected_factor_names[:10]):
        print(f"  {i+1:2d}. {factor_name}")

    # 最终数据集
    # 此时 combined_df 已经包含了所有需要的列
    final_combined_df = combined_df.copy()
    
    print(f"最终数据集: {len(final_combined_df)} 行 × {len(final_combined_df.columns)} 列")
    print(f"其中因子数: {len(selected_factor_names)}, 标签数: {len(labels_df.columns)}")
    
    if len(final_combined_df) < 500:
        print("❌ 筛选后数据量不足")
        return None
    
    # 6. 创建时间序列
    print("\n第六步: 创建时间序列")
    sequence_length = 60
    features = final_combined_df[selected_factor_names]
    labels_final = final_combined_df[[f'return_{p}min' for p in target_periods]]
    
    X, y = [], []
    timestamps = []
    
    for i in range(sequence_length, len(features)):
        # 确保标签在未来，不会泄露
        if i + max(target_periods) <= len(features):
            feature_seq = features.iloc[i-sequence_length:i].values
            
            # 使用与 features 对齐的索引来获取标签
            # 确保 labels_final 的索引与 features 的索引一致
            label_timestamp = features.index[i]
            if label_timestamp in labels_final.index:
                label_vals = labels_final.loc[label_timestamp].values
                
                if not pd.isna(feature_seq).any() and not pd.isna(label_vals).any():
                    X.append(feature_seq)
                    y.append(label_vals)
                    timestamps.append(label_timestamp)

    X = np.array(X, dtype=np.float32)
    y = np.array(y, dtype=np.float32)
    
    print(f"序列数据: X={X.shape}, y={y.shape}")
    print(f"输入特征维度: {X.shape[2]} (因子数)")
    
    # 7. 时间序列分割
    print("\n第七步: 时间序列分割")
    n = len(X)
    train_end = int(n * 0.7)
    val_end = int(n * 0.85)
    
    X_train, y_train = X[:train_end], y[:train_end]
    X_val, y_val = X[train_end:val_end], y[train_end:val_end]
    X_test, y_test = X[val_end:], y[val_end:]
    timestamps_test = timestamps[val_end:]
    
    print(f"训练集: {X_train.shape}")
    print(f"验证集: {X_val.shape}")
    print(f"测试集: {X_test.shape}")
    
    # 8. 特征标准化
    print("\n第八步: 特征标准化")
    n_features = X_train.shape[2]
    X_train_flat = X_train.reshape(-1, n_features)
    
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train_flat).reshape(X_train.shape)
    X_val_scaled = scaler.transform(X_val.reshape(-1, n_features)).reshape(X_val.shape)
    X_test_scaled = scaler.transform(X_test.reshape(-1, n_features)).reshape(X_test.shape)
    
    # 9. 模型训练
    print("\n第九步: 模型训练")
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    model = SimpleGRU(
        input_size=n_features,
        hidden_size=64,
        num_layers=1,
        dropout=0.1,
        num_targets=len(target_periods)
    ).to(device)
    
    # 训练
    batch_size = 32
    train_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_train_scaled), torch.FloatTensor(y_train)),
        batch_size=batch_size, shuffle=True
    )
    val_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_val_scaled), torch.FloatTensor(y_val)),
        batch_size=batch_size
    )
    
    trainer = MLTrainer(model, device)
    trainer.model_save_path = 'best_improved_model.pth'
    trainer.train_model(train_loader, val_loader, epochs=30, patience=5)
    
    # 10. 专业评估
    print("\n第十步: 专业评估")
    test_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_test_scaled), torch.FloatTensor(y_test)),
        batch_size=batch_size
    )
    predictions, actuals = trainer.predict(test_loader)
    
    evaluator = ReturnPredictionEvaluator(transaction_cost=0.0005)
    target_names = [f'return_{p}min' for p in target_periods]
    
    evaluation_results = evaluator.evaluate_return_predictions(
        predictions, actuals, target_names, 
        timestamps=timestamps_test
    )
    
    print("\n✅ 改进的训练管道完成！")
    print(f"🎯 关键改进:")
    print(f"  - 因子选择: 使用全部 {len(selected_factor_names)} 个因子（跳过筛选）")
    print(f"  - 模型输入维度: {n_features}")
    
    return {
        'model': model,
        'trainer': trainer,
        'predictions': predictions,
        'actuals': actuals,
        'evaluation_results': evaluation_results,
        'target_names': target_names,
        'scaler': scaler,
        'selected_factor_names': selected_factor_names,
    }


class RollingWindowEvaluator:
    """滚动窗口评估器"""
    
    def __init__(self, window_size=1000, step_size=100, min_samples=500):
        self.window_size = window_size
        self.step_size = step_size
        self.min_samples = min_samples
        
    def rolling_model_evaluation(self, predictions, actuals, timestamps, target_names):
        """滚动窗口模型评估"""
        print("📊 开始滚动窗口模型评估...")
        
        if len(predictions) < self.window_size:
            print(f"  警告: 数据量({len(predictions)})小于窗口大小({self.window_size})")
            return {}
        
        rolling_results = {}
        
        for target_idx, target_name in enumerate(target_names):
            print(f"  评估目标: {target_name}")
            
            target_predictions = predictions[:, target_idx]
            target_actuals = actuals[:, target_idx]
            
            # 滚动窗口评估
            window_results = self._rolling_window_metrics(
                target_predictions, target_actuals, timestamps
            )
            
            rolling_results[target_name] = window_results
        
        # 计算整体稳定性指标
        stability_metrics = self._calculate_model_stability(rolling_results)
        
        print(f"✅ 滚动窗口评估完成")
        return {
            'rolling_results': rolling_results,
            'stability_metrics': stability_metrics,
            'evaluation_config': {
                'window_size': self.window_size,
                'step_size': self.step_size,
                'total_windows': len(list(rolling_results.values())[0]['window_metrics']) if rolling_results else 0
            }
        }
    
    def _rolling_window_metrics(self, predictions, actuals, timestamps):
        """计算滚动窗口指标"""
        window_metrics = []
        window_timestamps = []
        
        for start_idx in range(0, len(predictions) - self.window_size + 1, self.step_size):
            end_idx = start_idx + self.window_size
            
            window_pred = predictions[start_idx:end_idx]
            window_actual = actuals[start_idx:end_idx]
            window_time = timestamps[start_idx:end_idx]
            
            # 计算窗口内的指标
            metrics = self._calculate_window_metrics(window_pred, window_actual)
            metrics['start_time'] = window_time[0]
            metrics['end_time'] = window_time[-1]
            metrics['window_center'] = window_time[len(window_time)//2]
            
            window_metrics.append(metrics)
            window_timestamps.append(metrics['window_center'])
        
        return {
            'window_metrics': window_metrics,
            'window_timestamps': window_timestamps,
            'performance_trend': self._analyze_performance_trend(window_metrics)
        }
    
    def _calculate_window_metrics(self, predictions, actuals):
        """计算单个窗口的指标"""
        # 基础指标
        mse = np.mean((predictions - actuals) ** 2)
        mae = np.mean(np.abs(predictions - actuals))
        rmse = np.sqrt(mse)
        
        # 相关性
        corr = np.corrcoef(predictions, actuals)[0, 1]
        if np.isnan(corr):
            corr = 0
        
        # 方向准确性
        pred_direction = np.sign(predictions)
        actual_direction = np.sign(actuals)
        direction_accuracy = np.mean(pred_direction == actual_direction)
        
        # 预测偏差
        bias = np.mean(predictions - actuals)
        
        # 预测分散度
        pred_std = np.std(predictions)
        actual_std = np.std(actuals)
        
        # 信息比率
        excess_return = predictions - np.mean(predictions)
        tracking_error = np.std(predictions - actuals)
        info_ratio = np.mean(excess_return) / (tracking_error + 1e-8)
        
        return {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'correlation': corr,
            'direction_accuracy': direction_accuracy,
            'bias': bias,
            'pred_std': pred_std,
            'actual_std': actual_std,
            'info_ratio': info_ratio,
            'sample_count': len(predictions)
        }
    
    def _analyze_performance_trend(self, window_metrics):
        """分析性能趋势"""
        if len(window_metrics) < 2:
            return {
                'trend_direction': 'stable',
                'trend_strength': 0,
                'performance_volatility': 0
            }
        
        # 提取关键指标的时间序列
        correlations = [m['correlation'] for m in window_metrics]
        mses = [m['mse'] for m in window_metrics]
        direction_accuracies = [m['direction_accuracy'] for m in window_metrics]
        
        # 计算趋势
        def calculate_trend(values):
            if len(values) < 2:
                return 0, 0
            
            x = np.arange(len(values))
            try:
                slope, intercept = np.polyfit(x, values, 1)
                return slope, np.corrcoef(x, values)[0, 1]
            except:
                return 0, 0
        
        corr_trend, corr_trend_strength = calculate_trend(correlations)
        mse_trend, mse_trend_strength = calculate_trend(mses)
        acc_trend, acc_trend_strength = calculate_trend(direction_accuracies)
        
        # 综合趋势评估
        overall_trend = (corr_trend - mse_trend + acc_trend) / 3
        overall_strength = (abs(corr_trend_strength) + abs(mse_trend_strength) + abs(acc_trend_strength)) / 3
        
        # 性能波动性
        performance_volatility = np.std(correlations) + np.std(direction_accuracies)
        
        return {
            'trend_direction': 'improving' if overall_trend > 0.001 else 'declining' if overall_trend < -0.001 else 'stable',
            'trend_strength': overall_strength,
            'performance_volatility': performance_volatility,
            'correlation_trend': corr_trend,
            'mse_trend': mse_trend,
            'accuracy_trend': acc_trend
        }
    
    def _calculate_model_stability(self, rolling_results):
        """计算模型整体稳定性"""
        if not rolling_results:
            return {}
        
        stability_metrics = {}
        
        for target_name, results in rolling_results.items():
            window_metrics = results['window_metrics']
            
            if len(window_metrics) < 2:
                continue
            
            # 提取指标序列
            correlations = [m['correlation'] for m in window_metrics]
            mses = [m['mse'] for m in window_metrics]
            direction_accuracies = [m['direction_accuracy'] for m in window_metrics]
            
            # 计算稳定性指标
            stability_metrics[target_name] = {
                'correlation_stability': 1 / (np.std(correlations) + 1e-8),
                'mse_stability': 1 / (np.std(mses) + 1e-8),
                'accuracy_stability': 1 / (np.std(direction_accuracies) + 1e-8),
                'mean_correlation': np.mean(correlations),
                'mean_mse': np.mean(mses),
                'mean_accuracy': np.mean(direction_accuracies),
                'correlation_range': np.max(correlations) - np.min(correlations),
                'mse_range': np.max(mses) - np.min(mses),
                'accuracy_range': np.max(direction_accuracies) - np.min(direction_accuracies)
            }
        
        return stability_metrics
    
    def rolling_factor_evaluation(self, factor_df, target_series, factor_names):
        """滚动窗口因子评估"""
        print("📊 开始滚动窗口因子评估...")
        
        if len(factor_df) < self.window_size:
            print(f"  警告: 数据量({len(factor_df)})小于窗口大小({self.window_size})")
            return {}
        
        rolling_factor_results = {}
        
        for factor_name in factor_names:
            if factor_name not in factor_df.columns:
                continue
            
            print(f"  评估因子: {factor_name}")
            
            factor_series = factor_df[factor_name]
            
            # 滚动窗口因子评估
            factor_results = self._rolling_factor_metrics(factor_series, target_series)
            
            rolling_factor_results[factor_name] = factor_results
        
        # 因子稳定性排名
        factor_rankings = self._rank_factor_stability(rolling_factor_results)
        
        print(f"✅ 滚动窗口因子评估完成")
        return {
            'rolling_factor_results': rolling_factor_results,
            'factor_rankings': factor_rankings,
            'evaluation_config': {
                'window_size': self.window_size,
                'step_size': self.step_size,
                'evaluated_factors': len(rolling_factor_results)
            }
        }
    
    def _rolling_factor_metrics(self, factor_series, target_series):
        """计算滚动窗口因子指标"""
        # 数据对齐
        common_index = factor_series.index.intersection(target_series.index)
        factor_aligned = factor_series.loc[common_index]
        target_aligned = target_series.loc[common_index]
        
        window_metrics = []
        window_timestamps = []
        
        for start_idx in range(0, len(factor_aligned) - self.window_size + 1, self.step_size):
            end_idx = start_idx + self.window_size
            
            window_factor = factor_aligned.iloc[start_idx:end_idx]
            window_target = target_aligned.iloc[start_idx:end_idx]
            
            # 去除NaN
            mask = ~(pd.isna(window_factor) | pd.isna(window_target))
            if mask.sum() < self.min_samples:
                continue
            
            clean_factor = window_factor[mask]
            clean_target = window_target[mask]
            
            # 计算窗口内的因子指标
            metrics = self._calculate_factor_window_metrics(clean_factor, clean_target)
            metrics['window_center'] = factor_aligned.index[start_idx + self.window_size // 2]
            
            window_metrics.append(metrics)
            window_timestamps.append(metrics['window_center'])
        
        return {
            'window_metrics': window_metrics,
            'window_timestamps': window_timestamps,
            'factor_trend': self._analyze_factor_trend(window_metrics)
        }
    
    def _calculate_factor_adaptation(self, state_factor_results):
        """计算因子状态适应性"""
        factor_adaptation = {}
        
        # 获取所有因子名称
        all_factors = set()
        for state_results in state_factor_results.values():
            all_factors.update(state_results['factor_performance'].keys())
        
        for factor_name in all_factors:
            factor_state_performance = {}
            
            # 收集该因子在各状态下的性能
            for state, results in state_factor_results.items():
                if factor_name in results['factor_performance']:
                    factor_state_performance[state] = results['factor_performance'][factor_name]
            
            if len(factor_state_performance) < 2:
                continue
            
            # 计算适应性指标
            ics = [perf['abs_ic'] for perf in factor_state_performance.values()]
            win_rates = [perf['win_rate'] for perf in factor_state_performance.values()]
            
            # 稳定性
            ic_stability = 1 / (np.std(ics) + 1e-8)
            win_rate_stability = 1 / (np.std(win_rates) + 1e-8)
            
            # 最佳状态
            best_ic_state = max(factor_state_performance.items(), key=lambda x: x[1]['abs_ic'])
            best_win_rate_state = max(factor_state_performance.items(), key=lambda x: x[1]['win_rate'])
            
            factor_adaptation[factor_name] = {
                'ic_stability': ic_stability,
                'win_rate_stability': win_rate_stability,
                'best_ic_state': best_ic_state[0],
                'best_ic_value': best_ic_state[1]['abs_ic'],
                'best_win_rate_state': best_win_rate_state[0],
                'best_win_rate_value': best_win_rate_state[1]['win_rate'],
                'overall_adaptability': (ic_stability + win_rate_stability) / 2,
                'state_performance': factor_state_performance
            }
        
        return factor_adaptation


# ==============================================================================
# 5. 数据管道模块 (新核心)
# ==============================================================================
class CryptoTimeSeriesDataset(Dataset):
    """一个高效的、用于时间序列数据的PyTorch Dataset"""
    def __init__(self, data_df, factor_cols, target_cols, sequence_length, config, state_cols=None):
        self.sequence_length = sequence_length
        self.config = config
        self.factor_cols = factor_cols
        self.target_cols = target_cols
        self.state_cols = state_cols

        self.factors = data_df[self.factor_cols].values.astype(np.float32)
        self.targets = data_df[self.target_cols].values.astype(np.float32)
        if self.state_cols:
            self.states = data_df[self.state_cols].values.astype(np.float32)
        else:
            self.states = None

        self.num_samples = len(self.factors) - self.sequence_length + 1
        
    def __len__(self):
        return self.num_samples
        
    def __getitem__(self, idx):
        end_idx = idx + self.sequence_length
        feature_seq = self.factors[idx:end_idx]
        target_val = self.targets[end_idx - 1]

        if self.config.MODEL_NAME == 'StateAdaptiveNetwork' and self.states is not None:
            state_val = self.states[end_idx - 1]
            return torch.from_numpy(feature_seq), torch.from_numpy(state_val), torch.from_numpy(target_val)
        else:
            return torch.from_numpy(feature_seq), torch.from_numpy(target_val)


def create_labels(df, periods, transaction_cost=0.002):
    """创建平衡的标签（提高交易成本阈值，改善类别平衡）"""
    labels = {}
    for period in periods:
        # 收益率标签
        future_return = df['close'].shift(-period) / df['close'] - 1
        labels[f'return_{period}min'] = future_return * 100
        
        # 方向标签 - 使用更高的交易成本阈值改善类别平衡
        # 只有收益率超过0.2%才算真正的上涨，提高标签质量
        labels[f'direction_{period}min'] = (future_return >= transaction_cost).astype(float)
        
        # 打印标签分布统计
        valid_direction = labels[f'direction_{period}min'].dropna()
        if len(valid_direction) > 0:
            up_ratio = valid_direction.mean()
            print(f"  {period}分钟标签分布 (阈值={transaction_cost:.1%}): 上涨{up_ratio:.1%}, 下跌{(1-up_ratio):.1%}")
            
            # 计算类别权重用于损失函数
            if up_ratio > 0 and up_ratio < 1:
                pos_weight = (1 - up_ratio) / up_ratio
                print(f"    建议正类权重: {pos_weight:.2f}")
    
    return pd.DataFrame(labels, index=df.index)

def prepare_data_and_loaders(config):
    """
    完整的、现代化的数据准备流程，包含因子缓存和无泄漏的缩放。
    """
    import time
    
    print("--- 步骤 1: 加载原始数据 ---")
    df = pd.read_pickle(config.DATA_PATH)
    print(f"原始数据: {df.shape[0]} 行, 时间从 {df.index.min()} 到 {df.index.max()}")

    # --- 特征工程（使用因子缓存） ---
    print("\n--- 步骤 2: 特征工程（智能缓存） ---")
    
    # 创建因子缓存管理器
    factor_cache_manager = FactorCacheManager(config)
    
    # 使用缓存管理器获取或计算因子
    all_factors_df = factor_cache_manager.get_or_calculate_factors(df)
    
    # 定义状态特征名称（当前为空，因为没有启用状态特征）
    state_feature_names = []

    # --- 标签生成 ---
    print("\n--- 步骤 3: 生成标签 ---")
    labels_df = create_labels(df, config.TARGET_PERIODS)

    # --- 合并与对齐 ---
    print("\n--- 步骤 4: 合并与对齐数据 ---")
    combined_df = pd.concat([all_factors_df, labels_df], axis=1)
    combined_df = combined_df.dropna()
    print(f"对齐后数据: {combined_df.shape[0]} 行")

    # --- 安全数据分割 ---
    print("\n--- 步骤 5: 时间序列安全分割 ---")
    splitter = TimeSeriesSafeDataSplitter(train_ratio=config.TRAIN_RATIO, val_ratio=config.VAL_RATIO)
    splits = splitter.split_data(combined_df)
    train_df, val_df, test_df = splits['train'], splits['validation'], splits['test']

    # --- 无泄漏数据缩放 ---
    print("\n--- 步骤 6: 执行无未来信息泄露的数据缩放 ---")
    
    # 完善因子名称收集
    factor_names = [col for col in all_factors_df.columns if col not in state_feature_names]
    target_names = list(labels_df.columns)

    # 1. 在训练集上 fit 缩放器
    scaler = RobustScaler()
    scaler.fit(train_df[factor_names])
    print("  Scaler 在训练集上完成 fit。")

    # 2. 在所有数据集上 transform
    train_df[factor_names] = scaler.transform(train_df[factor_names])
    val_df[factor_names] = scaler.transform(val_df[factor_names])
    test_df[factor_names] = scaler.transform(test_df[factor_names])
    print("  训练、验证、测试集已使用相同的 scaler 完成 transform。")
    
    # --- 创建DataLoaders ---
    print("\n--- 步骤 7: 创建 DataLoaders ---")
    state_cols_for_loader = state_feature_names if config.MODEL_NAME == 'StateAdaptiveNetwork' else None

    train_dataset = CryptoTimeSeriesDataset(train_df, factor_names, target_names, config.SEQUENCE_LENGTH, config, state_cols_for_loader)
    val_dataset = CryptoTimeSeriesDataset(val_df, factor_names, target_names, config.SEQUENCE_LENGTH, config, state_cols_for_loader)
    test_dataset = CryptoTimeSeriesDataset(test_df, factor_names, target_names, config.SEQUENCE_LENGTH, config, state_cols_for_loader)

    train_loader = DataLoader(train_dataset, batch_size=config.BATCH_SIZE, shuffle=True, num_workers=config.NUM_WORKERS, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=config.BATCH_SIZE, shuffle=False, num_workers=config.NUM_WORKERS)
    test_loader = DataLoader(test_dataset, batch_size=config.BATCH_SIZE, shuffle=False, num_workers=config.NUM_WORKERS)
    
    print(f"DataLoaders 创建完毕。训练样本数: {len(train_dataset)}, 验证样本数: {len(val_dataset)}, 测试样本数: {len(test_dataset)}")
    
    return train_loader, val_loader, test_loader, factor_names, target_names, state_feature_names


# ==============================================================================
# 6. 训练器模块 (新核心)
# ==============================================================================
class Trainer:
    """增强版训练器 - 解决收敛问题和胜率优化"""
    def __init__(self, model, optimizer, criterion, config):
        self.model = model.to(config.DEVICE)
        self.optimizer = optimizer
        self.criterion = criterion
        self.config = config
        self.device = config.DEVICE
        self.model_save_path = config.MODEL_SAVE_PATH
        
        # 新增：训练统计
        self.train_losses = []
        self.val_losses = []
        self.direction_accuracies = []
        
        # 学习率调度器 - 改善收敛
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
        
        # 新增：学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=20,        # 初始周期
            T_mult=2,      # 周期倍数
            eta_min=config.LEARNING_RATE / 100
        )

    def _run_epoch(self, data_loader, is_training=True):
        """增强版epoch运行 - 增加方向准确率统计"""
        self.model.train() if is_training else self.model.eval()
        total_loss = 0.0
        total_direction_acc = 0.0
        num_batches = 0
        
        epoch_bar = tqdm(data_loader, desc="Training" if is_training else "Validation", leave=False)

        for batch in epoch_bar:
            # 动态解包输入
            if self.config.MODEL_NAME == 'StateAdaptiveNetwork':
                feature_seq, state_val, target_val = [d.to(self.device) for d in batch]
            else:
                feature_seq, target_val = [d.to(self.device) for d in batch]

            with torch.set_grad_enabled(is_training):
                if self.config.MODEL_NAME == 'StateAdaptiveNetwork':
                    outputs = self.model(feature_seq, state_val)
                else:
                    outputs = self.model(feature_seq)
                
                loss = self.criterion(outputs, target_val)

                if is_training:
                    self.optimizer.zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    self.optimizer.step()
            
            # 计算方向准确率（只计算方向预测部分）
            with torch.no_grad():
                pred_direction = torch.sigmoid(outputs[:, 1:2]) > 0.5  # 方向预测通过sigmoid
                target_direction = target_val[:, 1:2] > 0.5            # 方向目标
                direction_acc = (pred_direction == target_direction).float().mean().item()
                total_direction_acc += direction_acc
            
            total_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            epoch_bar.set_postfix({
                'loss': f'{loss.item():.6f}',
                'dir_acc': f'{direction_acc:.2%}'
            })
            
        avg_loss = total_loss / num_batches
        avg_direction_acc = total_direction_acc / num_batches
        
        return avg_loss, avg_direction_acc

    def fit(self, train_loader, val_loader):
        """增强版训练循环 - 综合优化收敛性和胜率"""
        best_val_loss = float('inf')
        best_direction_acc = 0.0
        epochs_no_improve = 0
        
        print(f"--- 开始增强训练，共 {self.config.EPOCHS} 个 epochs ---")
        print(f"--- 模型参数量: {sum(p.numel() for p in self.model.parameters()):,} ---")
        
        for epoch in range(self.config.EPOCHS):
            # 训练阶段
            train_loss, train_dir_acc = self._run_epoch(train_loader, is_training=True)
            
            # 验证阶段
            val_loss, val_dir_acc = self._run_epoch(val_loader, is_training=False)
            
            # 更新学习率调度器 - 基于验证损失
            self.scheduler.step(val_loss)
            
            # 记录统计信息
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.direction_accuracies.append(val_dir_acc)
            
            # 打印详细信息
            current_lr = self.scheduler.get_last_lr()[0]
            print(f"Epoch {epoch+1:>{len(str(self.config.EPOCHS))}}/{self.config.EPOCHS} | "
                  f"Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f} | "
                  f"Train Dir Acc: {train_dir_acc:.2%} | Val Dir Acc: {val_dir_acc:.2%} | "
                  f"LR: {current_lr:.6f}")

            # 综合评估改进 - 考虑损失和方向准确率
            improvement = False
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                improvement = True
            
            if val_dir_acc > best_direction_acc:
                best_direction_acc = val_dir_acc
                improvement = True
            
            if improvement:
                epochs_no_improve = 0
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'scheduler_state_dict': self.scheduler.state_dict(),
                    'epoch': epoch,
                    'val_loss': val_loss,
                    'direction_acc': val_dir_acc,
                    'train_losses': self.train_losses,
                    'val_losses': self.val_losses,
                    'direction_accuracies': self.direction_accuracies
                }, self.model_save_path)
                print(f"  ✅ 模型改进！Val Loss: {best_val_loss:.6f}, Dir Acc: {best_direction_acc:.2%}")
            else:
                epochs_no_improve += 1
            
            if epochs_no_improve >= self.config.PATIENCE:
                print(f"  Early stopping triggered after {self.config.PATIENCE} epochs with no improvement.")
                break
        
        print("--- 增强训练完成 ---")
        
        # 加载最佳模型
        checkpoint = torch.load(self.model_save_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        print(f"✅ 已加载最佳模型:")
        print(f"   最佳验证损失: {best_val_loss:.6f}")
        print(f"   最佳方向准确率: {best_direction_acc:.2%}")
        print(f"   训练轮数: {len(self.train_losses)}")
        
        return {
            'best_val_loss': best_val_loss,
            'best_direction_acc': best_direction_acc,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'direction_accuracies': self.direction_accuracies
        }

    def predict(self, data_loader):
        self.model.eval()
        all_predictions = []
        all_actuals = []
        
        with torch.no_grad():
            for batch in tqdm(data_loader, desc="Predicting", leave=False):
                if self.config.MODEL_NAME == 'StateAdaptiveNetwork':
                    feature_seq, _, target_val = [d.to(self.device) for d in batch]
                    state_val = batch[1].to(self.device)
                    outputs = self.model(feature_seq, state_val)
                else:
                    feature_seq, target_val = [d.to(self.device) for d in batch]
                    outputs = self.model(feature_seq)

                all_predictions.append(outputs.cpu().numpy())
                all_actuals.append(target_val.cpu().numpy())
                
        return np.concatenate(all_predictions, axis=0), np.concatenate(all_actuals, axis=0)


# ==============================================================================
# 7. 主流程编排模块
# ==============================================================================
def benchmark_factor_performance(df_sample=None, sample_size=10000):
    """性能基准测试函数"""
    import time
    
    print("🔬 开始因子计算性能基准测试...")
    
    # 如果没有提供测试数据，创建一个小样本
    if df_sample is None:
        print(f"  创建 {sample_size} 行测试数据...")
        dates = pd.date_range('2023-01-01', periods=sample_size, freq='1min')
        np.random.seed(42)
        df_sample = pd.DataFrame({
            'open': 100 + np.random.randn(sample_size).cumsum() * 0.1,
            'high': 100 + np.random.randn(sample_size).cumsum() * 0.1 + np.random.rand(sample_size) * 2,
            'low': 100 + np.random.randn(sample_size).cumsum() * 0.1 - np.random.rand(sample_size) * 2,
            'close': 100 + np.random.randn(sample_size).cumsum() * 0.1,
            'volume': np.random.randint(1000, 10000, sample_size)
        }, index=dates)
        df_sample['high'] = np.maximum(df_sample['high'], df_sample[['open', 'close']].max(axis=1))
        df_sample['low'] = np.minimum(df_sample['low'], df_sample[['open', 'close']].min(axis=1))
    
    print(f"  测试数据规模: {len(df_sample)} 行")
    
    # 测试AdvancedFactorGenerator性能
    print("  测试 AdvancedFactorGenerator 性能...")
    afg = AdvancedFactorGenerator()
    
    start_time = time.time()
    advanced_factors = afg.generate_all_factors(df_sample)
    advanced_time = time.time() - start_time
    
    print(f"    AdvancedFactorGenerator: 生成 {len(advanced_factors.columns)} 个因子，耗时 {advanced_time:.2f}秒")
    print(f"    平均每个因子耗时: {advanced_time/len(advanced_factors.columns):.4f}秒")
    print(f"    每行数据处理速度: {len(df_sample)/advanced_time:.0f} 行/秒")
    
    # 内存使用情况
    memory_usage = advanced_factors.memory_usage(deep=True).sum() / 1024 / 1024
    print(f"    内存使用: {memory_usage:.2f} MB")
    
    print("✅ 性能基准测试完成")
    return {
        'factor_count': len(advanced_factors.columns),
        'total_time': advanced_time,
        'rows_per_second': len(df_sample)/advanced_time,
        'memory_mb': memory_usage
    }

def run_pipeline():
    """协调整个端到端训练和评估流程的主函数"""
    config = Config()
    print(f"--- 流程开始，使用配置: {config.MODEL_NAME} ---")

    # 1. 准备数据
    train_loader, val_loader, test_loader, feature_names, target_names, state_feature_names = prepare_data_and_loaders(config)
    
    # 2. 初始化模型
    print("\n--- 步骤 8: 初始化模型 ---")
    if config.MODEL_NAME == 'StateAdaptiveNetwork':
        model = StateAdaptiveNetwork(
            input_size=len(feature_names),
            hidden_size=config.MODEL_HIDDEN_SIZE,
            num_heads=config.MODEL_NUM_HEADS,
            num_layers=config.MODEL_NUM_LAYERS,
            dropout=config.MODEL_DROPOUT,
            state_feature_size=len(state_feature_names)
        )
    elif config.MODEL_NAME == 'MultiScaleTCNTransformer':
        model = MultiScaleTCNTransformer(
            input_size=len(feature_names),
            hidden_size=config.MODEL_HIDDEN_SIZE,
            num_heads=config.MODEL_NUM_HEADS,
            num_layers=config.MODEL_NUM_LAYERS,
            dropout=config.MODEL_DROPOUT,
            tcn_channels=config.TCN_CHANNELS,
            kernel_sizes=config.TCN_KERNEL_SIZES
        )
    elif config.MODEL_NAME == 'EnhancedMultiScaleTCN':
        model = EnhancedMultiScaleTCN(
            input_size=len(feature_names),
            hidden_size=config.MODEL_HIDDEN_SIZE,
            num_heads=config.MODEL_NUM_HEADS,
            num_layers=config.MODEL_NUM_LAYERS,
            dropout=config.MODEL_DROPOUT,
            tcn_channels=config.TCN_CHANNELS,
            kernel_sizes=config.TCN_KERNEL_SIZES
        )
    elif config.MODEL_NAME == 'FinancialTransformerXL':
        model = FinancialTransformerXL(
            input_size=len(feature_names),
            d_model=config.TRANSFORMER_D_MODEL,
            n_head=config.MODEL_NUM_HEADS,
            n_layers=config.MODEL_NUM_LAYERS,
            d_ff=config.TRANSFORMER_D_FF,
            dropout=config.MODEL_DROPOUT,
            mem_len=config.TRANSFORMER_MEM_LEN
        )
    elif config.MODEL_NAME == 'WaveNetForFinance':
        model = WaveNetForFinance(
            input_size=len(feature_names),
            residual_channels=config.WAVENET_RESIDUAL_CHANNELS,
            dilation_channels=config.WAVENET_DILATION_CHANNELS,
            num_stacks=config.WAVENET_NUM_STACKS
        )
    else: # 'StableGRU'
        model = StableGRU(
            input_size=len(feature_names),
            hidden_size=config.MODEL_HIDDEN_SIZE,
            num_layers=config.MODEL_NUM_LAYERS,
            dropout=config.MODEL_DROPOUT,
            num_targets=len(target_names)
        )
    print(f"模型 {config.MODEL_NAME} 已创建。参数量: {sum(p.numel() for p in model.parameters()):,}")

    # 3. 初始化优化器和增强损失函数
    optimizer = optim.AdamW(  # 使用AdamW优化器
        model.parameters(), 
        lr=config.LEARNING_RATE,
        weight_decay=0.01,  # 权重衰减
        betas=(0.9, 0.999),
        eps=1e-8
    )
    # 选择损失函数策略
    print(f"📊 损失函数选择策略:")
    
    # 从训练数据中估算类别分布
    sample_batch = next(iter(train_loader))
    sample_targets = sample_batch[1] if len(sample_batch) == 2 else sample_batch[2]
    direction_targets = sample_targets[:, 1]  # 方向标签
    up_ratio = direction_targets.mean().item()
    
    print(f"   类别分布: 上涨{up_ratio:.1%}, 下跌{(1-up_ratio):.1%}")
    
    # 选择损失函数类型
    loss_type = config.LOSS_TYPE if hasattr(config, 'LOSS_TYPE') else 'profit_focused'
    
    if loss_type == 'profit_focused':
        print(f"   使用利润导向损失函数 (ProfitFocusedLoss)")
        criterion = ProfitFocusedLoss(transaction_cost=0.002)
    elif loss_type == 'trading_optimized':
        print(f"   使用交易优化损失函数 (TradingOptimizedLoss)")
        if up_ratio > 0 and up_ratio < 1:
            pos_weight = (1 - up_ratio) / up_ratio
            print(f"   应用正类权重: {pos_weight:.2f}")
            criterion = TradingOptimizedLoss(transaction_cost=0.002, pos_weight=pos_weight)
        else:
            criterion = TradingOptimizedLoss(transaction_cost=0.002)
    else:  # 传统方法
        print(f"   使用传统混合损失函数")
        if up_ratio > 0 and up_ratio < 1:
            pos_weight = (1 - up_ratio) / up_ratio
            criterion = TradingOptimizedLoss(pos_weight=pos_weight)
        else:
            criterion = TradingOptimizedLoss()

    # 4. 训练增强模型
    print("\n--- 步骤 9: 训练增强模型 ---")
    trainer = Trainer(model, optimizer, criterion, config)
    training_stats = trainer.fit(train_loader, val_loader)

    # 5. 在测试集上评估
    print("\n--- 步骤 10: 在测试集上评估 ---")
    predictions, actuals = trainer.predict(test_loader)
    
    # 6. 增强性能分析
    print("\n--- 步骤 11: 增强性能分析 ---")
    
    # 计算整体方向准确率 - 修复版
    overall_direction_acc = 0
    for i in range(len(target_names)):
        if 'return' in target_names[i]:
            # 对收益率预测计算方向准确率
            pred_direction = np.sign(predictions[:, i])
            actual_direction = np.sign(actuals[:, i])
            direction_acc = np.mean(pred_direction == actual_direction)
        else:
            # 对方向预测计算分类准确率
            pred_direction = (torch.sigmoid(torch.tensor(predictions[:, i])) > 0.5).numpy().astype(int)
            actual_direction = actuals[:, i].astype(int)
            direction_acc = np.mean(pred_direction == actual_direction)
        
        overall_direction_acc += direction_acc
        print(f"{target_names[i]} 方向准确率: {direction_acc:.2%}")
    
    overall_direction_acc /= len(target_names)
    print(f"\n🎯 整体方向准确率: {overall_direction_acc:.2%}")
    print(f"📈 训练改进效果:")
    print(f"   最终验证损失: {training_stats['best_val_loss']:.6f}")
    print(f"   最佳方向准确率: {training_stats['best_direction_acc']:.2%}")
    print(f"   实际训练轮数: {len(training_stats['train_losses'])}")
    
    # 7. 生成详细性能报告 - 修复版
    def evaluate_mixed_predictions(predictions, actuals, target_names):
        """评估混合预测（收益率+方向）"""
        print("🎯 混合预测评估（收益率+方向）")
        print("="*60)
        
        for i, target_name in enumerate(target_names):
            pred_vals = predictions[:, i]
            actual_vals = actuals[:, i]
            
            print(f"\n📈 {target_name} 详细评估:")
            print("-" * 50)
            
            if 'return' in target_name:
                # 收益率预测评估
                mse = np.mean((pred_vals - actual_vals) ** 2)
                corr = np.corrcoef(pred_vals, actual_vals)[0, 1] if len(pred_vals) > 1 else 0
                
                # 方向准确率
                pred_direction = np.sign(pred_vals)
                actual_direction = np.sign(actual_vals)
                direction_acc = np.mean(pred_direction == actual_direction)
                
                # 交易策略 - 基于收益率预测
                buy_signals = pred_vals > 0
                if buy_signals.sum() > 0:
                    strategy_returns = actual_vals[buy_signals]
                    win_rate = np.mean(strategy_returns > 0)
                    avg_profit = np.mean(strategy_returns[strategy_returns > 0]) if np.sum(strategy_returns > 0) > 0 else 0
                    avg_loss = np.mean(np.abs(strategy_returns[strategy_returns <= 0])) if np.sum(strategy_returns <= 0) > 0 else 0
                    profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
                    total_return = np.sum(strategy_returns)
                else:
                    win_rate = 0
                    profit_loss_ratio = 0
                    total_return = 0
                    buy_signals = np.zeros_like(pred_vals, dtype=bool)
                
                print(f"  基础指标:")
                print(f"    MSE: {mse:.6f}")
                print(f"    相关性: {corr:.4f}")
                print(f"    方向准确率: {direction_acc:.2%}")
                print(f"  交易策略:")
                print(f"    交易次数: {buy_signals.sum()}")
                print(f"    胜率: {win_rate:.2%}")
                print(f"    盈亏比: {profit_loss_ratio:.2f}")
                print(f"    总收益: {total_return:.2f}%")
                
            else:
                # 方向预测评估
                pred_direction = (torch.sigmoid(torch.tensor(pred_vals)) > 0.5).numpy().astype(int)
                actual_direction = actual_vals.astype(int)
                direction_acc = np.mean(pred_direction == actual_direction)
                
                # 统计分布
                unique_pred, counts_pred = np.unique(pred_direction, return_counts=True)
                unique_actual, counts_actual = np.unique(actual_direction, return_counts=True)
                
                print(f"  分类指标:")
                print(f"    准确率: {direction_acc:.2%}")
                print(f"    预测分布: {dict(zip(unique_pred, counts_pred))}")
                print(f"    实际分布: {dict(zip(unique_actual, counts_actual))}")
                
                # 上涨下跌准确率
                up_mask = actual_direction == 1
                down_mask = actual_direction == 0
                up_acc = np.mean(pred_direction[up_mask] == actual_direction[up_mask]) if up_mask.sum() > 0 else 0
                down_acc = np.mean(pred_direction[down_mask] == actual_direction[down_mask]) if down_mask.sum() > 0 else 0
                
                print(f"    上涨准确率: {up_acc:.2%} ({up_mask.sum()}样本)")
                print(f"    下跌准确率: {down_acc:.2%} ({down_mask.sum()}样本)")
    
    evaluate_mixed_predictions(predictions, actuals, target_names)
    
    # 8. 保存增强结果
    enhanced_results = {
        'predictions': predictions,
        'actuals': actuals,
        'training_stats': training_stats,
        'model_config': {
            'model_name': config.MODEL_NAME,
            'hidden_size': config.MODEL_HIDDEN_SIZE,
            'num_layers': config.MODEL_NUM_LAYERS,
            'dropout': config.MODEL_DROPOUT,
            'learning_rate': config.LEARNING_RATE,
            'batch_size': config.BATCH_SIZE,
            'epochs': config.EPOCHS
        },
        'performance_summary': {
            'overall_direction_accuracy': overall_direction_acc,
            'best_val_loss': training_stats['best_val_loss'],
            'best_direction_acc': training_stats['best_direction_acc'],
            'training_epochs': len(training_stats['train_losses'])
        }
    }
    
    print("\n--- 流程执行完毕 ---")
    print("🚀 增强模型升级完成！")
    print(f"✅ 模型复杂度大幅提升:")
    print(f"   参数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   隐藏层大小: {config.MODEL_HIDDEN_SIZE}")
    print(f"   网络层数: {config.MODEL_NUM_LAYERS}")
    print(f"   使用模型: {config.MODEL_NAME}")
    
    # 保存详细结果
    with open(config.RESULTS_SAVE_PATH, 'wb') as f:
        pickle.dump(enhanced_results, f)
    print(f"📊 详细评估结果已保存到 {config.RESULTS_SAVE_PATH}")
    
    return enhanced_results

# ==============================================================================
# 9. 执行入口
# ==============================================================================
# ==============================================================================
# 10. 因子缓存管理辅助函数
# ==============================================================================

def clear_factor_cache():
    """清除因子缓存的便利函数"""
    config = Config()
    cache_manager = FactorCacheManager(config)
    cache_manager.clear_cache()

def force_recalculate_factors():
    """强制重新计算因子的便利函数"""
    config = Config()
    config.FORCE_RECALCULATE_FACTORS = True
    
    print("🔄 将在下次运行时强制重新计算因子")
    print("提示: 你也可以直接在Config类中设置 FORCE_RECALCULATE_FACTORS = True")
    
    return config

def check_factor_cache_status():
    """检查因子缓存状态"""
    config = Config()
    cache_manager = FactorCacheManager(config)
    
    print("📊 因子缓存状态检查:")
    print(f"  缓存启用: {config.ENABLE_FACTOR_CACHE}")
    print(f"  缓存路径: {config.FACTOR_CACHE_PATH}")
    print(f"  强制重算: {config.FORCE_RECALCULATE_FACTORS}")
    
    if os.path.exists(config.FACTOR_CACHE_PATH):
        try:
            with open(config.FACTOR_CACHE_PATH, 'rb') as f:
                cache_data = pickle.load(f)
            
            print(f"  缓存文件: ✅ 存在")
            print(f"  缓存时间: {cache_data.get('timestamp', '未知')}")
            print(f"  因子数量: {cache_data['factors_df'].shape[1] if 'factors_df' in cache_data else '未知'}")
            print(f"  数据行数: {cache_data['factors_df'].shape[0] if 'factors_df' in cache_data else '未知'}")
            
            if 'data_info' in cache_data:
                data_info = cache_data['data_info']
                print(f"  数据时间: {data_info.get('start_time', '未知')} 到 {data_info.get('end_time', '未知')}")
                print(f"  数据形状: {data_info.get('shape', '未知')}")
                
        except Exception as e:
            print(f"  缓存文件: ❌ 损坏 ({e})")
    else:
        print(f"  缓存文件: ❌ 不存在")

if __name__ == "__main__":
    # 检查缓存状态
    print("🔍 检查因子缓存状态...")
    check_factor_cache_status()
    
    # 可选：清除缓存（如果需要）
    # print("\n🗑️ 清除因子缓存...")
    # clear_factor_cache()
    
    # 可选：运行性能基准测试
    # benchmark_results = benchmark_factor_performance(sample_size=5000)
    
    # 运行主流程
    print("\n🚀 运行主训练流程...")
    results = run_pipeline()


# class MarketStateDetector:
#     """市场状态检测器 - 已更新为价量突破逻辑"""
    
#     def __init__(self, trend_threshold=0.6, volatility_threshold=0.3):
#         # 这个 init 方法现在只是为了保持兼容性，实际参数在 Config 中定义
#         pass

#     def detect_breakout_state(self, df, config):
#         """
#         检测价量配合的突破状态。
#         """
#         print("  ...检测价量配合突破状态...")
#         if 'volume' not in df.columns or 'close' not in df.columns:
#             print("    警告: 无 'close' 或 'volume' 数据，无法执行价量突破检测。返回全零状态。")
#             return pd.Series(0, index=df.index, name='breakout_state')

#         price_ma = df['close'].rolling(window=config.BREAKOUT_MA_PERIOD, min_periods=config.BREAKOUT_MA_PERIOD // 2).mean()
#         volume_ma = df['volume'].rolling(window=config.BREAKOUT_VOLUME_MA_PERIOD, min_periods=config.BREAKOUT_VOLUME_MA_PERIOD // 2).mean()
        
#         price_breakout = df['close'] > price_ma
#         volume_confirmation = df['volume'] > (volume_ma * config.BREAKOUT_VOLUME_MULTIPLIER)
        
#         breakout_state = price_breakout & volume_confirmation
        
#         print(f"    - 在 {len(df)} 个时间点中，检测到 {breakout_state.sum()} 个突破信号。")
#         return breakout_state.astype(int).rename('breakout_state')

# # ... (StateAdaptiveNetwork and other classes after MarketStateDetector) ...

# # ... (inside prepare_data_and_loaders function) ...
#     # ... after AdvancedFactorGenerator block ...

#     # c. 市场状态特征 (使用新的价量突破逻辑)
#     print("  c. 计算市场状态特征 (MarketStateDetector - 价量突破)...")
#     msd = MarketStateDetector()
#     breakout_state_series = msd.detect_breakout_state(df, config)
#     state_features_df = breakout_state_series.to_frame()
#     state_feature_names = ['breakout_state']
#     factor_list.append(state_features_df)
    
#     # 合并所有初步特征
#     all_factors_df = pd.concat(factor_list, axis=1)

#     # d. 交互因子 (可选)
#     # ... (rest of the function is the same)



class WaveNetForFinance(nn.Module):
    """
    金融专用WaveNet模型
    使用膨胀卷积捕捉多时间尺度模式
    """
    
    def __init__(self, input_size, residual_channels=64, dilation_channels=64,
                 skip_channels=64, end_channels=128, num_layers=10, 
                 num_stacks=3, kernel_size=2):
        super(WaveNetForFinance, self).__init__()
        
        self.input_size = input_size
        self.residual_channels = residual_channels
        self.num_layers = num_layers
        self.num_stacks = num_stacks
        
        # 输入卷积
        self.start_conv = nn.Conv1d(input_size, residual_channels, kernel_size=1)
        
        # 膨胀卷积层
        self.dilated_convs = nn.ModuleList()
        self.residual_convs = nn.ModuleList()
        self.skip_convs = nn.ModuleList()
        
        for stack in range(num_stacks):
            for layer in range(num_layers):
                dilation = 2 ** layer
                
                # 膨胀卷积
                dilated_conv = nn.Conv1d(
                    residual_channels, dilation_channels, 
                    kernel_size, dilation=dilation, padding=dilation
                )
                self.dilated_convs.append(dilated_conv)
                
                # 残差连接
                residual_conv = nn.Conv1d(dilation_channels, residual_channels, kernel_size=1)
                self.residual_convs.append(residual_conv)
                
                # 跳跃连接
                skip_conv = nn.Conv1d(dilation_channels, skip_channels, kernel_size=1)
                self.skip_convs.append(skip_conv)
        
        # 输出层
        self.end_conv1 = nn.Conv1d(skip_channels, end_channels, kernel_size=1)
        self.end_conv2 = nn.Conv1d(end_channels, 6, kernel_size=1)  # 6个预测目标
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
    def forward(self, x):
        """前向传播"""
        # 转换维度: (batch, seq_len, features) -> (batch, features, seq_len)
        x = x.transpose(1, 2)
        
        # 起始卷积
        x = self.start_conv(x)
        skip_connections = []
        
        # 膨胀卷积块
        for i in range(len(self.dilated_convs)):
            residual = x
            
            # 膨胀卷积
            x = self.dilated_convs[i](x)
            x = torch.tanh(x) * torch.sigmoid(x)  # Gated activation
            
            # 跳跃连接
            skip = self.skip_convs[i](x)
            skip_connections.append(skip)
            
            # 残差连接
            x = self.residual_convs[i](x)
            x = x + residual
        
        # 聚合跳跃连接
        skip_sum = torch.stack(skip_connections, dim=0).sum(dim=0)
        
        # 输出层
        x = torch.relu(skip_sum)
        x = self.end_conv1(x)
        x = torch.relu(x)
        x = self.end_conv2(x)
        
        # 全局池化并压缩
        x = self.global_pool(x).squeeze(-1)
        
        return x


class PretrainedTimeSeriesModels:
    """
    预训练时间序列模型集成类
    支持Chronos、TimesFM等预训练模型的集成使用
    """
    
    def __init__(self, model_type='chronos', model_size='base'):
        self.model_type = model_type
        self.model_size = model_size
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def load_chronos_model(self):
        """加载Chronos预训练模型"""
        try:
            from chronos import ChronosPipeline
            
            model_map = {
                'tiny': 'amazon/chronos-t5-tiny',
                'mini': 'amazon/chronos-t5-mini', 
                'small': 'amazon/chronos-t5-small',
                'base': 'amazon/chronos-t5-base',
                'large': 'amazon/chronos-t5-large',
                'bolt-tiny': 'amazon/chronos-bolt-tiny',
                'bolt-mini': 'amazon/chronos-bolt-mini',
                'bolt-small': 'amazon/chronos-bolt-small',
                'bolt-base': 'amazon/chronos-bolt-base'
            }
            
            model_name = model_map.get(self.model_size, 'amazon/chronos-t5-base')
            print(f"🚀 加载Chronos模型: {model_name}")
            
            self.model = ChronosPipeline.from_pretrained(
                model_name,
                device_map=self.device,
                torch_dtype=torch.bfloat16 if self.device.type == 'cuda' else torch.float32
            )
            
            print(f"✅ Chronos模型加载成功")
            return True
            
        except ImportError:
            print("❌ 请安装chronos库: pip install git+https://github.com/amazon-science/chronos-forecasting.git")
            return False
        except Exception as e:
            print(f"❌ 加载Chronos模型失败: {e}")
            return False
    
    def load_timesfm_model(self):
        """加载TimesFM预训练模型"""
        try:
            import timesfm
            
            print(f"🚀 加载TimesFM模型")
            
            self.model = timesfm.TimesFm(
                context_len=512,
                horizon_len=120,
                input_patch_len=32,
                output_patch_len=128,
                num_layers=20,
                model_dims=1280,
                backend='gpu' if self.device.type == 'cuda' else 'cpu'
            )
            
            self.model.load_from_checkpoint(repo_id="google/timesfm-1.0-200m")
            
            print(f"✅ TimesFM模型加载成功")
            return True
            
        except ImportError:
            print("❌ 请安装timesfm库: pip install timesfm")
            return False
        except Exception as e:
            print(f"❌ 加载TimesFM模型失败: {e}")
            return False
    
    def predict_with_chronos(self, time_series_data, prediction_length=120):
        """使用Chronos模型进行预测"""
        if self.model is None:
            if not self.load_chronos_model():
                return None
        
        try:
            # 确保输入是tensor格式
            if isinstance(time_series_data, np.ndarray):
                context = torch.tensor(time_series_data, dtype=torch.float32)
            elif isinstance(time_series_data, list):
                context = torch.tensor(time_series_data, dtype=torch.float32)
            else:
                context = time_series_data
            
            # 预测
            forecast = self.model.predict(context, prediction_length)
            
            # 返回预测结果的统计信息
            forecast_np = forecast[0].numpy()  # 取第一个序列的预测
            
            # 计算分位数
            low = np.quantile(forecast_np, 0.1, axis=0)
            median = np.quantile(forecast_np, 0.5, axis=0)
            high = np.quantile(forecast_np, 0.9, axis=0)
            
            return {
                'predictions': forecast_np,
                'median': median,
                'lower_bound': low,
                'upper_bound': high,
                'point_forecast': median  # 使用中位数作为点预测
            }
            
        except Exception as e:
            print(f"❌ Chronos预测失败: {e}")
            return None
    
    def predict_with_timesfm(self, time_series_data, prediction_length=120):
        """使用TimesFM模型进行预测"""
        if self.model is None:
            if not self.load_timesfm_model():
                return None
        
        try:
            # TimesFM需要特定的输入格式
            if isinstance(time_series_data, np.ndarray):
                forecast_input = [time_series_data]
            else:
                forecast_input = [np.array(time_series_data)]
            
            # 设置频率 (0=高频, 1=中频, 2=低频)
            frequency_input = [0]  # 分钟级数据使用高频
            
            # 预测
            point_forecast, quantile_forecast = self.model.forecast(
                forecast_input,
                freq=frequency_input
            )
            
            return {
                'point_forecast': point_forecast[0],  # 点预测
                'quantile_forecast': quantile_forecast[0] if quantile_forecast is not None else None
            }
            
        except Exception as e:
            print(f"❌ TimesFM预测失败: {e}")
            return None
    
    def create_ensemble_predictions(self, time_series_data, prediction_length=120):
        """创建集成预测"""
        predictions = {}
        
        # Chronos预测
        chronos_result = self.predict_with_chronos(time_series_data, prediction_length)
        if chronos_result:
            predictions['chronos'] = chronos_result['point_forecast']
        
        # TimesFM预测
        timesfm_result = self.predict_with_timesfm(time_series_data, prediction_length)
        if timesfm_result:
            predictions['timesfm'] = timesfm_result['point_forecast']
        
        # 如果有多个模型的预测，计算集成结果
        if len(predictions) > 1:
            # 简单平均集成
            ensemble_pred = np.mean(list(predictions.values()), axis=0)
            predictions['ensemble'] = ensemble_pred
        
        return predictions


def demonstrate_pretrained_models():
    """演示预训练模型的使用"""
    print("🚀 预训练时间序列模型演示")
    print("="*60)
    
    # 创建示例数据
    np.random.seed(42)
    sample_data = np.cumsum(np.random.randn(500)) + 100  # 模拟价格序列
    
    # 初始化预训练模型集成器
    pretrained_models = PretrainedTimeSeriesModels()
    
    # 使用Chronos进行预测
    print("\n📊 使用Chronos模型预测...")
    chronos_results = pretrained_models.predict_with_chronos(
        sample_data, prediction_length=60
    )
    
    if chronos_results:
        print(f"✅ Chronos预测成功")
        print(f"  预测长度: {len(chronos_results['point_forecast'])}")
        print(f"  预测均值: {np.mean(chronos_results['point_forecast']):.4f}")
        print(f"  预测标准差: {np.std(chronos_results['point_forecast']):.4f}")
    
    # 使用TimesFM进行预测
    print("\n📊 使用TimesFM模型预测...")
    timesfm_results = pretrained_models.predict_with_timesfm(
        sample_data, prediction_length=60
    )
    
    if timesfm_results:
        print(f"✅ TimesFM预测成功")
        print(f"  预测长度: {len(timesfm_results['point_forecast'])}")
        print(f"  预测均值: {np.mean(timesfm_results['point_forecast']):.4f}")
        print(f"  预测标准差: {np.std(timesfm_results['point_forecast']):.4f}")
    
    # 创建集成预测
    print("\n🎯 创建集成预测...")
    ensemble_results = pretrained_models.create_ensemble_predictions(
        sample_data, prediction_length=60
    )
    
    print(f"✅ 可用预测模型: {list(ensemble_results.keys())}")
    
    return ensemble_results


def integrate_pretrained_with_pipeline(df, pretrained_predictions=None):
    """将预训练模型集成到现有管道中"""
    print("🔗 集成预训练模型到训练管道")
    print("="*60)
    
    if pretrained_predictions is None:
        print("⚠️  没有提供预训练模型预测，跳过集成")
        return df
    
    # 这里可以将预训练模型的预测作为额外特征
    # 或者作为集成学习的一部分
    
    print("✅ 预训练模型集成完成")
    return df


# 使用说明和推荐配置
COMPLEX_MODEL_RECOMMENDATIONS = """
🎯 复杂模型推荐配置

1. **MultiScaleTCNTransformer** (推荐用于中等数据量)
   - 参数量: ~500K-2M
   - 优势: 结合CNN局部特征和Transformer全局建模
   - 适用: 序列长度120-512，特征数50-200
   - 配置: MODEL_NAME = 'MultiScaleTCNTransformer'

2. **FinancialTransformerXL** (推荐用于大数据量)
   - 参数量: ~5M-20M  
   - 优势: 超长序列建模，记忆机制
   - 适用: 序列长度>300，需要长期依赖建模
   - 配置: MODEL_NAME = 'FinancialTransformerXL'

3. **WaveNetForFinance** (推荐用于高频数据)
   - 参数量: ~1M-5M
   - 优势: 膨胀卷积，多时间尺度特征提取
   - 适用: 高频交易数据，需要快速推理
   - 配置: MODEL_NAME = 'WaveNetForFinance'

4. **预训练模型集成** (推荐用于生产环境)
   - Chronos-Bolt-Base: 零样本预测能力强
   - TimesFM: 谷歌开发，性能稳定
   - 优势: 无需训练，直接使用，泛化能力强

📝 使用建议:
- 数据量<10万: 使用预训练模型
- 数据量10万-100万: MultiScaleTCNTransformer
- 数据量>100万: FinancialTransformerXL
- 高频交易: WaveNetForFinance
- 生产环境: 预训练模型+微调
"""

if __name__ == "__main__":
    print(COMPLEX_MODEL_RECOMMENDATIONS)
    
    # 演示预训练模型
    # demonstrate_pretrained_models()
    
    results = run_pipeline()


# 保持向后兼容
