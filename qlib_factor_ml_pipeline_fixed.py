#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版：qlib因子机器学习管道
彻底解决未来信息泄露问题
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# qlib相关导入
try:
    import qlib
    from qlib import init
    from qlib.data import D
    from qlib.data.ops import *
    from qlib.contrib.data.handler import Alpha158, Alpha360
    from qlib.data.dataset.handler import DataHandlerLP
    QLIB_AVAILABLE = True
    print("qlib可用")
except ImportError:
    QLIB_AVAILABLE = False
    print("qlib不可用，将使用简化版因子计算")


class TimeAwareFactorExtractor:
    """时间感知的因子提取器 - 避免未来信息泄露"""
    
    def __init__(self):
        pass
    
    def calculate_factors_safe(self, df):
        """安全的因子计算 - 不使用未来信息"""
        print("使用时间安全的因子计算...")
        
        factors = {}
        
        # ================ 基础价格因子 ================
        periods = [1, 2, 3, 5, 10, 15, 20, 30, 60, 120]
        for period in periods:
            # 收益率（向后看，安全）
            factors[f'return_{period}'] = df['close'].pct_change(period)
            
            # 移动平均（向后看，安全）
            factors[f'ma_{period}'] = df['close'].rolling(period, min_periods=1).mean()
            
            # 价格相对位置（向后看，安全）
            factors[f'price_vs_ma_{period}'] = df['close'] / factors[f'ma_{period}'] - 1
            
            # 标准差（向后看，安全）
            factors[f'std_{period}'] = df['close'].rolling(period, min_periods=1).std()
            
            # 最高价和最低价（向后看，安全）
            factors[f'max_{period}'] = df['high'].rolling(period, min_periods=1).max()
            factors[f'min_{period}'] = df['low'].rolling(period, min_periods=1).min()
            
            # 价格排名（向后看，安全）
            factors[f'rank_{period}'] = df['close'].rolling(period, min_periods=1).rank() / period
            
            # 价格位置（向后看，安全）
            high_max = df['high'].rolling(period, min_periods=1).max()
            low_min = df['low'].rolling(period, min_periods=1).min()
            factors[f'price_position_{period}'] = (df['close'] - low_min) / (high_max - low_min + 1e-8)
        
        # ================ 成交量因子 ================
        if 'volume' in df.columns:
            for period in periods:
                factors[f'volume_ma_{period}'] = df['volume'].rolling(period, min_periods=1).mean()
                factors[f'volume_ratio_{period}'] = df['volume'] / (factors[f'volume_ma_{period}'] + 1e-8)
                factors[f'volume_std_{period}'] = df['volume'].rolling(period, min_periods=1).std()
                factors[f'turnover_{period}'] = (df['volume'] * df['close']).rolling(period, min_periods=1).sum()
        
        # ================ RSI因子 ================
        for period in [6, 12, 14, 21, 24]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(period, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period, min_periods=1).mean()
            rs = gain / (loss + 1e-8)
            factors[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # ================ MACD系列 ================
        for fast, slow in [(12, 26), (5, 35), (8, 21)]:
            ema_fast = df['close'].ewm(span=fast, min_periods=1).mean()
            ema_slow = df['close'].ewm(span=slow, min_periods=1).mean()
            macd = ema_fast - ema_slow
            factors[f'macd_{fast}_{slow}'] = macd
            factors[f'macd_signal_{fast}_{slow}'] = macd.ewm(span=9, min_periods=1).mean()
            factors[f'macd_hist_{fast}_{slow}'] = macd - factors[f'macd_signal_{fast}_{slow}']
        
        # ================ 布林带因子 ================
        for period in [20, 60]:
            sma = df['close'].rolling(period, min_periods=1).mean()
            std = df['close'].rolling(period, min_periods=1).std()
            factors[f'bb_upper_{period}'] = sma + 2 * std
            factors[f'bb_lower_{period}'] = sma - 2 * std
            factors[f'bb_position_{period}'] = (df['close'] - sma) / (2 * std + 1e-8)
            factors[f'bb_width_{period}'] = (4 * std) / (sma + 1e-8)
        
        # ================ ATR因子 ================
        for period in [14, 28]:
            tr = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            factors[f'atr_{period}'] = tr.rolling(period, min_periods=1).mean()
            factors[f'atr_ratio_{period}'] = factors[f'atr_{period}'] / (df['close'] + 1e-8)
        
        # ================ 动量因子 ================
        for period in [1, 3, 5, 10, 20]:
            factors[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
            factors[f'roc_{period}'] = (df['close'] - df['close'].shift(period)) / (df['close'].shift(period) + 1e-8)
        
        # ================ 波动率因子 ================
        for period in [5, 10, 20, 60]:
            returns = df['close'].pct_change()
            factors[f'volatility_{period}'] = returns.rolling(period, min_periods=1).std()
            
            # 已实现波动率（使用高低价，向后看，安全）
            log_hl = np.log((df['high'] + 1e-8) / (df['low'] + 1e-8))
            factors[f'realized_vol_{period}'] = np.sqrt((log_hl ** 2).rolling(period, min_periods=1).mean())
        
        # ================ 趋势因子 ================
        for period in [5, 10, 20, 60]:
            # 趋势强度（线性回归斜率，向后看，安全）
            def trend_strength(x):
                if len(x) < 3:
                    return 0
                try:
                    return np.corrcoef(x, np.arange(len(x)))[0, 1]
                except:
                    return 0
            
            factors[f'trend_strength_{period}'] = df['close'].rolling(period, min_periods=3).apply(trend_strength)
        
        # ================ 价量配合因子 ================
        if 'volume' in df.columns:
            for period in [5, 10, 20]:
                price_change = df['close'].pct_change()
                volume_change = df['volume'].pct_change()
                factors[f'price_volume_corr_{period}'] = price_change.rolling(period, min_periods=1).corr(volume_change)
                
                # OBV指标（向后看，安全）
                obv = (df['volume'] * np.sign(df['close'].diff())).cumsum()
                factors[f'obv_ma_{period}'] = obv.rolling(period, min_periods=1).mean()
                factors[f'obv_momentum_{period}'] = obv / obv.shift(period) - 1
        
        # ================ K线形态因子 ================
        # Doji形态
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low'] + 1e-8
        factors['doji'] = (body_size / total_range < 0.1).astype(int)
        
        # 上下影线比例
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        factors['upper_shadow_ratio'] = upper_shadow / total_range
        factors['lower_shadow_ratio'] = lower_shadow / total_range
        factors['body_ratio'] = body_size / total_range
        
        # 缺口因子
        factors['gap_up'] = (df['open'] > df['high'].shift(1)).astype(int)
        factors['gap_down'] = (df['open'] < df['low'].shift(1)).astype(int)
        factors['gap_size'] = (df['open'] - df['close'].shift(1)) / (df['close'].shift(1) + 1e-8)
        
        # ⚠️ 移除了center=True的局部极值因子，因为它们会泄露未来信息
        # 改用向后看的极值检测
        for period in [3, 5, 10]:
            # 只向后看的局部极值（安全）
            factors[f'high_vs_max_{period}'] = df['high'] / df['high'].rolling(period, min_periods=1).max()
            factors[f'low_vs_min_{period}'] = df['low'] / df['low'].rolling(period, min_periods=1).min()
        
        # ================ 相对强度因子 ================
        for period in [10, 20]:
            # 价格在历史分布中的位置（向后看，安全）
            price_rank = df['close'].rolling(period, min_periods=1).rank()
            factors[f'price_percentile_{period}'] = price_rank / period
            
            # 成交量在历史分布中的位置
            if 'volume' in df.columns:
                volume_rank = df['volume'].rolling(period, min_periods=1).rank()
                factors[f'volume_percentile_{period}'] = volume_rank / period
        
        print(f"安全计算了 {len(factors)} 个因子（无未来信息泄露）")
        return pd.DataFrame(factors, index=df.index)


class TimeAwareMLDataProcessor:
    """时间感知的机器学习数据处理器"""
    
    def __init__(self, feature_columns, target_periods=[10, 30, 60, 120, 240, 360]):
        self.feature_columns = feature_columns
        self.target_periods = target_periods
        self.scalers = {}
        
    def create_labels(self, df):
        """创建标签 - 确保时间对齐正确"""
        labels = {}
        
        print("创建标签 - 验证时间对齐...")
        
        for period in self.target_periods:
            # 🎯 关键：未来收益率标签
            # shift(-period) 表示向前看period分钟的收益率
            # 这是正确的，因为我们要预测未来的收益
            future_price = df['close'].shift(-period)  # 未来price
            current_price = df['close']                # 当前price
            future_return = (future_price - current_price) / current_price
            
            labels[f'return_{period}min'] = future_return
            
            # 验证：打印几个示例来确认时间对齐
            if period == 10:  # 只验证10分钟
                print(f"时间对齐验证（{period}分钟）:")
                for i in range(5, 10):  # 检查前几个样本
                    curr_time = df.index[i]
                    if i + period < len(df):
                        future_time = df.index[i + period]
                        curr_p = df.iloc[i]['close']
                        fut_p = df.iloc[i + period]['close']
                        calc_ret = (fut_p - curr_p) / curr_p
                        label_ret = future_return.iloc[i]
                        
                        print(f"  {curr_time} -> {future_time}: 价格 {curr_p:.2f}->{fut_p:.2f}, "
                              f"计算收益={calc_ret:.4f}, 标签收益={label_ret:.4f}")
            
            # 分类标签（三分类：下跌、震荡、上涨）
            labels[f'direction_{period}min'] = pd.cut(
                future_return, 
                bins=[-np.inf, -0.005, 0.005, np.inf], 
                labels=[0, 1, 2]  # 0:下跌, 1:震荡, 2:上涨
            ).astype(float)
        
        return pd.DataFrame(labels, index=df.index)
    
    def create_sequences(self, features, labels, sequence_length=60):
        """创建序列数据 - 验证时间对齐"""
        X, y = [], []
        timestamps = []  # 记录时间戳用于验证
        
        print(f"创建序列数据，序列长度={sequence_length}")
        
        for i in range(sequence_length, len(features)):
            # 特征序列：使用过去sequence_length个时间点的数据
            feature_seq = features.iloc[i-sequence_length:i].values
            
            # 标签：当前时刻i对应的未来收益率
            label_values = labels.iloc[i].values
            
            # 验证：确保没有NaN标签
            if not np.isnan(label_values).any():
                X.append(feature_seq)
                y.append(label_values)
                timestamps.append(features.index[i])
        
        print(f"生成 {len(X)} 个有效序列")
        
        # 验证时间对齐（打印几个例子）
        print("序列时间对齐验证:")
        for i in range(min(3, len(timestamps))):
            seq_end_time = timestamps[i]
            print(f"  序列 {i}: 特征序列结束于 {seq_end_time}, 标签对应未来收益率")
        
        return np.array(X), np.array(y), timestamps
    
    def split_data_time_aware(self, X, y, timestamps, train_ratio=0.7, val_ratio=0.15):
        """时间序列分割 - 严格按时间顺序"""
        n = len(X)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        # 按时间顺序分割
        X_train = X[:train_end]
        y_train = y[:train_end]
        ts_train = timestamps[:train_end]
        
        X_val = X[train_end:val_end]
        y_val = y[train_end:val_end]
        ts_val = timestamps[train_end:val_end]
        
        X_test = X[val_end:]
        y_test = y[val_end:]
        ts_test = timestamps[val_end:]
        
        print(f"数据分割:")
        print(f"  训练集: {len(X_train)} 样本, 时间范围: {ts_train[0]} ~ {ts_train[-1]}")
        print(f"  验证集: {len(X_val)} 样本, 时间范围: {ts_val[0]} ~ {ts_val[-1]}")
        print(f"  测试集: {len(X_test)} 样本, 时间范围: {ts_test[0]} ~ {ts_test[-1]}")
        
        return (X_train, y_train, ts_train), (X_val, y_val, ts_val), (X_test, y_test, ts_test)
    
    def normalize_features_rolling(self, X_train, X_val, X_test, ts_train, ts_val, ts_test, window_size=252):
        """滚动标准化 - 避免使用未来信息"""
        print(f"应用滚动标准化，窗口大小={window_size}")
        
        # 重塑数据
        n_train, seq_len, n_features = X_train.shape
        X_train_reshaped = X_train.reshape(-1, n_features)
        X_val_reshaped = X_val.reshape(-1, n_features)
        X_test_reshaped = X_test.reshape(-1, n_features)
        
        # 初始化标准化后的数组
        X_train_scaled = np.zeros_like(X_train_reshaped)
        X_val_scaled = np.zeros_like(X_val_reshaped)
        X_test_scaled = np.zeros_like(X_test_reshaped)
        
        # 🎯 关键：对训练集进行滚动标准化
        for i in range(len(X_train)):
            # 使用过去window_size个样本的统计量
            start_idx = max(0, i - window_size)
            end_idx = i  # 不包含当前样本
            
            if end_idx > start_idx:
                # 计算历史统计量
                historical_data = X_train_reshaped[start_idx * seq_len:end_idx * seq_len]
                if len(historical_data) > 0:
                    mean_hist = np.mean(historical_data, axis=0)
                    std_hist = np.std(historical_data, axis=0) + 1e-8
                    
                    # 标准化当前样本
                    current_sample = X_train_reshaped[i * seq_len:(i + 1) * seq_len]
                    X_train_scaled[i * seq_len:(i + 1) * seq_len] = (current_sample - mean_hist) / std_hist
                else:
                    # 如果没有历史数据，保持原值
                    X_train_scaled[i * seq_len:(i + 1) * seq_len] = X_train_reshaped[i * seq_len:(i + 1) * seq_len]
        
        # 🎯 关键：对验证集和测试集使用展开窗口标准化
        # 验证集：可以使用训练集的统计量
        if len(X_train) > 0:
            # 使用训练集最后window_size个样本的统计量
            start_idx = max(0, len(X_train) - window_size)
            train_stats_data = X_train_reshaped[start_idx * seq_len:]
            train_mean = np.mean(train_stats_data, axis=0)
            train_std = np.std(train_stats_data, axis=0) + 1e-8
            
            X_val_scaled = (X_val_reshaped - train_mean) / train_std
        
        # 测试集：使用训练集+验证集的统计量
        if len(X_train) > 0 and len(X_val) > 0:
            # 合并训练集和验证集数据计算统计量
            combined_data = np.vstack([X_train_reshaped, X_val_reshaped])
            start_idx = max(0, len(combined_data) - window_size * seq_len)
            stats_data = combined_data[start_idx:]
            combined_mean = np.mean(stats_data, axis=0)
            combined_std = np.std(stats_data, axis=0) + 1e-8
            
            X_test_scaled = (X_test_reshaped - combined_mean) / combined_std
        
        # 重塑回原始形状
        X_train_scaled = X_train_scaled.reshape(X_train.shape)
        X_val_scaled = X_val_scaled.reshape(X_val.shape)
        X_test_scaled = X_test_scaled.reshape(X_test.shape)
        
        print("✅ 滚动标准化完成，无未来信息泄露")
        return X_train_scaled, X_val_scaled, X_test_scaled


# 使用之前的模型定义
class MultiTargetLSTM(nn.Module):
    """多目标LSTM模型"""
    
    def __init__(self, input_size, hidden_size=128, num_layers=2, dropout=0.2, num_targets=12):
        super(MultiTargetLSTM, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            batch_first=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, num_targets)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        lstm_out, (hidden, cell) = self.lstm(x)
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        last_output = attn_out[:, -1, :]
        out = self.dropout(last_output)
        out = self.relu(self.fc1(out))
        out = self.fc2(out)
        return out


class MultiTargetTransformer(nn.Module):
    """多目标Transformer模型"""
    
    def __init__(self, input_size, d_model=128, nhead=8, num_layers=3, dropout=0.1, num_targets=12):
        super(MultiTargetTransformer, self).__init__()
        
        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)
        self.pos_encoding = nn.Parameter(torch.randn(1000, d_model))
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(d_model, d_model // 2)
        self.fc2 = nn.Linear(d_model // 2, num_targets)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        seq_len = x.size(1)
        x = self.input_projection(x)
        x = x + self.pos_encoding[:seq_len, :].unsqueeze(0)
        transformer_out = self.transformer(x)
        last_output = transformer_out[:, -1, :]
        out = self.dropout(last_output)
        out = self.relu(self.fc1(out))
        out = self.fc2(out)
        return out


class MLTrainer:
    """机器学习训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def train_model(self, train_loader, val_loader, epochs=100, lr=0.001, patience=15):
        """训练模型"""
        criterion = nn.MSELoss()
        optimizer = optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        print("开始训练模型...")
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                train_loss += loss.item()
            
            # 验证阶段
            self.model.eval()
            val_loss = 0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X = batch_X.to(self.device)
                    batch_y = batch_y.to(self.device)
                    
                    outputs = self.model(batch_X)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
            
            train_loss /= len(train_loader)
            val_loss /= len(val_loader)
            
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_model_fixed.pth')
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch:3d}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch}')
                break
        
        self.model.load_state_dict(torch.load('best_model_fixed.pth'))
        print("训练完成！")
        
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                outputs = self.model(batch_X)
                predictions.append(outputs.cpu().numpy())
                actuals.append(batch_y.cpu().numpy())
        
        predictions = np.vstack(predictions)
        actuals = np.vstack(actuals)
        
        return predictions, actuals


def main_pipeline_fixed(df):
    """修复版主要流程 - 无未来信息泄露"""
    print("="*70)
    print("🛡️ 修复版qlib量化机器学习流程 - 无未来信息泄露")
    print("="*70)
    
    # 1. 安全的因子计算
    print("步骤1: 安全因子计算（无未来信息）...")
    factor_extractor = TimeAwareFactorExtractor()
    factors_df = factor_extractor.calculate_factors_safe(df)
    
    print(f"生成 {len(factors_df.columns)} 个安全因子")
    
    # 2. 创建标签
    print("步骤2: 创建标签...")
    processor = TimeAwareMLDataProcessor(factors_df.columns)
    labels_df = processor.create_labels(df)
    
    # 3. 数据清理
    print("步骤3: 数据清理...")
    combined_df = pd.concat([factors_df, labels_df], axis=1)
    
    # 删除缺失值过多的因子
    factor_na_counts = factors_df.isnull().sum()
    threshold = len(factors_df) * 0.3  # 允许30%缺失
    good_factors = factor_na_counts[factor_na_counts < threshold].index
    
    # 前向填充并删除剩余NaN
    combined_df = combined_df.fillna(method='ffill').dropna()
    
    features = combined_df[good_factors]
    labels = combined_df[labels_df.columns]
    
    print(f"有效数据: {len(combined_df)} 行, {len(good_factors)} 个因子")
    
    if len(combined_df) < 100:
        print("❌ 数据量不足")
        return None
    
    # 4. 创建序列数据
    print("步骤4: 创建序列数据...")
    sequence_length = min(60, len(combined_df) // 10)
    X, y, timestamps = processor.create_sequences(features, labels, sequence_length)
    
    if len(X) < 50:
        print("❌ 序列数据不足")
        return None
    
    # 5. 时间感知数据分割
    print("步骤5: 时间感知数据分割...")
    (X_train, y_train, ts_train), (X_val, y_val, ts_val), (X_test, y_test, ts_test) = processor.split_data_time_aware(X, y, timestamps)
    
    # 6. 滚动标准化
    print("步骤6: 滚动标准化...")
    X_train, X_val, X_test = processor.normalize_features_rolling(
        X_train, X_val, X_test, ts_train, ts_val, ts_test
    )
    
    # 7. 创建数据加载器
    print("步骤7: 创建数据加载器...")
    batch_size = min(32, len(X_train) // 4)
    
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # 8. 训练模型
    input_size = X_train.shape[2]
    num_targets = y_train.shape[1]
    
    print(f"步骤8: 训练模型 (输入维度={input_size}, 目标维度={num_targets})...")
    
    # LSTM模型
    print("训练LSTM模型...")
    lstm_model = MultiTargetLSTM(input_size=input_size, num_targets=num_targets)
    lstm_trainer = MLTrainer(lstm_model)
    lstm_trainer.train_model(train_loader, val_loader, epochs=50)
    
    # Transformer模型
    print("训练Transformer模型...")
    transformer_model = MultiTargetTransformer(input_size=input_size, num_targets=num_targets)
    transformer_trainer = MLTrainer(transformer_model)
    transformer_trainer.train_model(train_loader, val_loader, epochs=50)
    
    # 9. 预测和评估
    print("步骤9: 预测和评估...")
    lstm_pred, lstm_actual = lstm_trainer.predict(test_loader)
    transformer_pred, transformer_actual = transformer_trainer.predict(test_loader)
    
    # 10. 结果分析
    target_names = [f'return_{p}min' for p in processor.target_periods] + \
                   [f'direction_{p}min' for p in processor.target_periods]
    
    print("\n" + "="*50)
    print("📊 修复版模型结果分析")
    print("="*50)
    
    print("\n=== LSTM模型结果 ===")
    for i, target in enumerate(target_names):
        if i < lstm_pred.shape[1]:
            mse = np.mean((lstm_pred[:, i] - lstm_actual[:, i]) ** 2)
            corr = np.corrcoef(lstm_pred[:, i], lstm_actual[:, i])[0, 1] if len(lstm_pred) > 1 else 0
            print(f"{target}: MSE={mse:.6f}, Corr={corr:.4f}")
    
    print("\n=== Transformer模型结果 ===")
    for i, target in enumerate(target_names):
        if i < transformer_pred.shape[1]:
            mse = np.mean((transformer_pred[:, i] - transformer_actual[:, i]) ** 2)
            corr = np.corrcoef(transformer_pred[:, i], transformer_actual[:, i])[0, 1] if len(transformer_pred) > 1 else 0
            print(f"{target}: MSE={mse:.6f}, Corr={corr:.4f}")
    
    print("\n✅ 修复版流程完成！所有未来信息泄露问题已解决。")
    
    return {
        'lstm_model': lstm_model,
        'transformer_model': transformer_model,
        'lstm_predictions': lstm_pred,
        'transformer_predictions': transformer_pred,
        'actual_values': lstm_actual,
        'feature_columns': good_factors.tolist(),
        'target_columns': target_names,
        'test_timestamps': ts_test
    }


if __name__ == "__main__":
    print("🛡️ 修复版qlib机器学习管道")
    print("主要修复:")
    print("1. 移除center=True的rolling操作")
    print("2. 实现滚动标准化，避免使用未来统计量")
    print("3. 验证标签时间对齐")
    print("4. 确保所有因子计算都是向后看的")
    print("-" * 50)
    
    # 示例使用
    try:
        # 这里需要你的数据加载函数
        # df = load_your_data()
        # results = main_pipeline_fixed(df)
        pass
    except Exception as e:
        print(f"示例执行失败: {e}")
        print("请提供数据后运行main_pipeline_fixed(df)") 