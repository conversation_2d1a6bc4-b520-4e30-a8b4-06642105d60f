#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型可视化脚本 - 展示GRU模型训练结果和性能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import pickle
import os
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_model_results():
    """加载模型结果"""
    try:
        # 如果有保存的详细结果
        if os.path.exists('model_results.pkl'):
            with open('model_results.pkl', 'rb') as f:
                return pickle.load(f)
        else:
            print("未找到详细结果文件，将重新运行模型...")
            return None
    except Exception as e:
        print(f"加载结果失败: {e}")
        return None

def plot_training_history(train_losses, val_losses, save_path='results/'):
    """绘制训练历史"""
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    plt.figure(figsize=(12, 5))
    
    # 训练损失
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='训练损失', color='blue')
    plt.plot(val_losses, label='验证损失', color='red')
    plt.title('模型训练损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 损失对比
    plt.subplot(1, 2, 2)
    epochs = range(len(train_losses))
    plt.plot(epochs, train_losses, 'b-', alpha=0.7, label='训练损失')
    plt.plot(epochs, val_losses, 'r-', alpha=0.7, label='验证损失')
    plt.fill_between(epochs, train_losses, alpha=0.3, color='blue')
    plt.fill_between(epochs, val_losses, alpha=0.3, color='red')
    plt.title('训练 vs 验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_path}/training_history.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_prediction_results(predictions, actuals, target_names, save_path='results/'):
    """绘制预测结果"""
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    # 选择收益率预测进行可视化
    return_targets = [name for name in target_names if 'return' in name]
    
    n_targets = len(return_targets)
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, target in enumerate(return_targets):
        if i >= 6:
            break
            
        target_idx = target_names.index(target)
        pred = predictions[:, target_idx]
        actual = actuals[:, target_idx]
        
        # 散点图
        ax = axes[i]
        ax.scatter(actual, pred, alpha=0.6, s=1, color='blue')
        
        # 完美预测线
        min_val = min(actual.min(), pred.min())
        max_val = max(actual.max(), pred.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='完美预测')
        
        # 计算相关性
        corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
        
        ax.set_xlabel('实际值')
        ax.set_ylabel('预测值')
        ax.set_title(f'{target}\n相关性: {corr:.3f}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        mse = np.mean((pred - actual) ** 2)
        ax.text(0.05, 0.95, f'MSE: {mse:.4f}', transform=ax.transAxes, 
                bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(f'{save_path}/prediction_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_time_series_predictions(predictions, actuals, target_names, save_path='results/'):
    """绘制时间序列预测"""
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    # 选择主要目标
    main_targets = ['return_10min', 'return_30min', 'return_60min']
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    for i, target in enumerate(main_targets):
        if target in target_names:
            target_idx = target_names.index(target)
            pred = predictions[:, target_idx]
            actual = actuals[:, target_idx]
            
            # 只显示前500个点，避免图表过于密集
            n_show = min(500, len(pred))
            x = range(n_show)
            
            axes[i].plot(x, actual[:n_show], label='实际值', alpha=0.7, color='blue')
            axes[i].plot(x, pred[:n_show], label='预测值', alpha=0.7, color='red')
            axes[i].set_title(f'{target} - 时间序列预测对比')
            axes[i].set_xlabel('时间步')
            axes[i].set_ylabel('收益率 (%)')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
            
            # 计算并显示相关性
            corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
            axes[i].text(0.02, 0.98, f'相关性: {corr:.3f}', transform=axes[i].transAxes,
                        bbox=dict(boxstyle="round", facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(f'{save_path}/time_series_predictions.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_performance_summary(predictions, actuals, target_names, save_path='results/'):
    """绘制性能总结"""
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    # 计算所有指标
    metrics = []
    for i, target in enumerate(target_names):
        pred = predictions[:, i]
        actual = actuals[:, i]
        
        mse = np.mean((pred - actual) ** 2)
        mae = np.mean(np.abs(pred - actual))
        corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
        
        # 方向准确性
        pred_direction = np.sign(pred)
        actual_direction = np.sign(actual)
        direction_accuracy = np.mean(pred_direction == actual_direction)
        
        metrics.append({
            'target': target,
            'mse': mse,
            'mae': mae,
            'correlation': corr if not np.isnan(corr) else 0,
            'direction_accuracy': direction_accuracy
        })
    
    df_metrics = pd.DataFrame(metrics)
    
    # 创建性能总结图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # MSE
    axes[0, 0].bar(range(len(df_metrics)), df_metrics['mse'], color='skyblue')
    axes[0, 0].set_title('均方误差 (MSE)')
    axes[0, 0].set_xticks(range(len(df_metrics)))
    axes[0, 0].set_xticklabels(df_metrics['target'], rotation=45, ha='right')
    axes[0, 0].grid(True, alpha=0.3)
    
    # MAE
    axes[0, 1].bar(range(len(df_metrics)), df_metrics['mae'], color='lightcoral')
    axes[0, 1].set_title('平均绝对误差 (MAE)')
    axes[0, 1].set_xticks(range(len(df_metrics)))
    axes[0, 1].set_xticklabels(df_metrics['target'], rotation=45, ha='right')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 相关性
    axes[1, 0].bar(range(len(df_metrics)), df_metrics['correlation'], color='lightgreen')
    axes[1, 0].set_title('相关系数')
    axes[1, 0].set_xticks(range(len(df_metrics)))
    axes[1, 0].set_xticklabels(df_metrics['target'], rotation=45, ha='right')
    axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 方向准确性
    axes[1, 1].bar(range(len(df_metrics)), df_metrics['direction_accuracy'], color='gold')
    axes[1, 1].set_title('方向准确性')
    axes[1, 1].set_xticks(range(len(df_metrics)))
    axes[1, 1].set_xticklabels(df_metrics['target'], rotation=45, ha='right')
    axes[1, 1].axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='随机水平')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_path}/performance_summary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return df_metrics

def create_comprehensive_report():
    """创建综合报告"""
    print("📊 生成GRU模型综合分析报告...")
    
    # 重新运行模型获取结果
    print("🔄 重新运行模型以获取详细结果...")
    
    # 导入并运行管道
    from qlib_factor_ml_pipeline import OptimizedMLPipeline
    import pandas as pd
    
    # 加载数据
    pkl_path = 'data/BTC_USDT_1m_20250313_20250412.pkl'
    if os.path.exists(pkl_path):
        data = pd.read_pickle(pkl_path)
        data = data.iloc[-10000:]  # 使用最后10000行数据
        
        # 运行管道
        pipeline = OptimizedMLPipeline()
        results = pipeline.run_complete_pipeline(data)
        
        if results:
            # 可视化结果
            print("\n📈 生成可视化图表...")
            
            # 训练历史
            if 'train_losses' in results and 'val_losses' in results:
                plot_training_history(results['train_losses'], results['val_losses'])
            
            # 预测结果
            plot_prediction_results(
                results['predictions'], 
                results['actuals'], 
                results['target_names']
            )
            
            # 时间序列预测
            plot_time_series_predictions(
                results['predictions'], 
                results['actuals'], 
                results['target_names']
            )
            
            # 性能总结
            df_metrics = plot_performance_summary(
                results['predictions'], 
                results['actuals'], 
                results['target_names']
            )
            
            # 保存指标到CSV
            df_metrics.to_csv('results/model_metrics.csv', index=False)
            
            print("\n✅ 综合报告生成完成！")
            print("📁 结果保存在 results/ 目录下:")
            print("  - training_history.png: 训练历史")
            print("  - prediction_results.png: 预测结果散点图")
            print("  - time_series_predictions.png: 时间序列预测")
            print("  - performance_summary.png: 性能总结")
            print("  - model_metrics.csv: 详细指标")
            
            return results
        else:
            print("❌ 模型运行失败")
            return None
    else:
        print(f"❌ 数据文件未找到: {pkl_path}")
        return None

if __name__ == "__main__":
    print("📊 GRU模型可视化分析工具")
    print("=" * 50)
    
    results = create_comprehensive_report()
    
    if results:
        print("\n🎯 模型性能总结:")
        print("=" * 50)
        
        # 打印关键指标
        predictions = results['predictions']
        actuals = results['actuals']
        target_names = results['target_names']
        
        for i, target in enumerate(target_names):
            if 'return' in target:  # 只显示收益率预测
                pred = predictions[:, i]
                actual = actuals[:, i]
                
                corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
                mse = np.mean((pred - actual) ** 2)
                
                # 方向准确性
                pred_direction = np.sign(pred)
                actual_direction = np.sign(actual)
                direction_accuracy = np.mean(pred_direction == actual_direction)
                
                print(f"\n{target}:")
                print(f"  相关性: {corr:.4f}")
                print(f"  MSE: {mse:.6f}")
                print(f"  方向准确性: {direction_accuracy:.2%}")
        
        print("\n📈 模型训练成功完成！")
        print("📁 所有可视化结果已保存到 results/ 目录") 