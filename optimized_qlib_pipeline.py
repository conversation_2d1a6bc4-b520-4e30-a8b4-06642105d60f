#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面优化版：qlib因子机器学习管道
解决所有数据处理和因子计算问题
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os
import pickle
from datetime import datetime
warnings.filterwarnings('ignore')

# qlib相关导入
try:
    import qlib
    from qlib import init
    from qlib.data import D
    from qlib.data.ops import *
    from qlib.contrib.data.handler import Alpha158, Alpha360
    from qlib.data.dataset.handler import DataHandlerLP
    QLIB_AVAILABLE = True
    print("✅ qlib可用")
except ImportError:
    QLIB_AVAILABLE = False
    print("⚠️ qlib不可用，将使用手动因子计算")


class OptimizedFactorEngine:
    """优化的因子引擎 - 高质量因子计算"""
    
    def __init__(self):
        self.factor_categories = {
            'price': [],
            'volume': [],
            'momentum': [],
            'volatility': [],
            'technical': [],
            'pattern': []
        }
        
    def calculate_all_factors(self, df):
        """计算所有优化因子"""
        print("🔧 启动优化因子计算引擎...")
        
        factors = {}
        
        # 1. 价格因子
        print("  计算价格因子...")
        price_factors = self._calculate_price_factors(df)
        factors.update(price_factors)
        
        # 2. 成交量因子
        print("  计算成交量因子...")
        volume_factors = self._calculate_volume_factors(df)
        factors.update(volume_factors)
        
        # 3. 动量因子
        print("  计算动量因子...")
        momentum_factors = self._calculate_momentum_factors(df)
        factors.update(momentum_factors)
        
        # 4. 波动率因子
        print("  计算波动率因子...")
        volatility_factors = self._calculate_volatility_factors(df)
        factors.update(volatility_factors)
        
        # 5. 技术指标因子
        print("  计算技术指标因子...")
        technical_factors = self._calculate_technical_factors(df)
        factors.update(technical_factors)
        
        # 6. 形态因子
        print("  计算形态因子...")
        pattern_factors = self._calculate_pattern_factors(df)
        factors.update(pattern_factors)
        
        # 转换为DataFrame
        factor_df = pd.DataFrame(factors, index=df.index)
        
        print(f"✅ 因子计算完成，共生成 {len(factor_df.columns)} 个因子")
        
        return factor_df
    
    def _calculate_price_factors(self, df):
        """计算价格相关因子"""
        factors = {}
        
        # 基础价格因子
        periods = [3, 5, 10, 15, 20, 30, 60, 120]
        
        for period in periods:
            # 移动平均
            ma = df['close'].rolling(period, min_periods=max(1, period//3)).mean()
            factors[f'ma_{period}'] = ma
            
            # 价格相对移动平均
            factors[f'price_vs_ma_{period}'] = df['close'] / ma - 1
            
            # 移动平均斜率
            factors[f'ma_slope_{period}'] = ma.diff(5) / ma.shift(5)
            
            # 价格通道位置
            high_max = df['high'].rolling(period, min_periods=max(1, period//3)).max()
            low_min = df['low'].rolling(period, min_periods=max(1, period//3)).min()
            factors[f'price_channel_{period}'] = (df['close'] - low_min) / (high_max - low_min + 1e-8)
            
            # 价格排名
            factors[f'price_rank_{period}'] = df['close'].rolling(period, min_periods=max(1, period//3)).rank() / period
        
        # EMA因子
        for span in [5, 10, 20, 50]:
            ema = df['close'].ewm(span=span).mean()
            factors[f'ema_{span}'] = ema
            factors[f'price_vs_ema_{span}'] = df['close'] / ema - 1
        
        # MACD系列
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        macd = ema12 - ema26
        signal = macd.ewm(span=9).mean()
        
        factors['macd'] = macd
        factors['macd_signal'] = signal
        factors['macd_histogram'] = macd - signal
        factors['macd_cross'] = (macd > signal).astype(int)
        
        return factors
    
    def _calculate_volume_factors(self, df):
        """计算成交量因子"""
        factors = {}
        
        if 'volume' not in df.columns:
            return factors
        
        periods = [5, 10, 20, 60]
        
        for period in periods:
            # 成交量移动平均
            vol_ma = df['volume'].rolling(period, min_periods=max(1, period//3)).mean()
            factors[f'volume_ma_{period}'] = vol_ma
            
            # 成交量相对比率
            factors[f'volume_ratio_{period}'] = df['volume'] / vol_ma
            
            # 成交量标准差
            factors[f'volume_std_{period}'] = df['volume'].rolling(period, min_periods=max(1, period//3)).std()
            
            # 成交量排名
            factors[f'volume_rank_{period}'] = df['volume'].rolling(period, min_periods=max(1, period//3)).rank() / period
        
        # 价量配合
        for period in [5, 10, 20]:
            price_change = df['close'].pct_change()
            volume_change = df['volume'].pct_change()
            factors[f'price_volume_corr_{period}'] = price_change.rolling(period, min_periods=max(1, period//3)).corr(volume_change)
        
        # OBV指标
        price_change = df['close'].diff()
        volume_direction = np.where(price_change > 0, df['volume'], 
                                  np.where(price_change < 0, -df['volume'], 0))
        obv = pd.Series(volume_direction, index=df.index).cumsum()
        
        factors['obv'] = obv
        factors['obv_ma_10'] = obv.rolling(10, min_periods=5).mean()
        factors['obv_slope'] = obv.diff(5) / obv.shift(5)
        
        return factors
    
    def _calculate_momentum_factors(self, df):
        """计算动量因子"""
        factors = {}
        
        # 收益率动量
        periods = [1, 2, 3, 5, 10, 15, 20, 30, 60]
        
        for period in periods:
            # 简单收益率
            factors[f'return_{period}'] = df['close'].pct_change(period)
            
            # 对数收益率
            factors[f'log_return_{period}'] = np.log(df['close'] / df['close'].shift(period))
            
            # 累积收益率
            factors[f'cum_return_{period}'] = (df['close'] / df['close'].shift(period) - 1)
        
        # 动量强度
        for period in [5, 10, 20]:
            # 上涨天数比例
            up_days = (df['close'] > df['close'].shift(1)).rolling(period, min_periods=max(1, period//3)).sum()
            factors[f'up_ratio_{period}'] = up_days / period
            
            # 连续上涨天数
            up_streak = (df['close'] > df['close'].shift(1)).astype(int)
            factors[f'up_streak_{period}'] = up_streak.rolling(period, min_periods=max(1, period//3)).sum()
        
        # ROC指标
        for period in [5, 10, 20]:
            factors[f'roc_{period}'] = (df['close'] - df['close'].shift(period)) / df['close'].shift(period) * 100
        
        return factors
    
    def _calculate_volatility_factors(self, df):
        """计算波动率因子"""
        factors = {}
        
        # 收益率波动率
        returns = df['close'].pct_change()
        
        periods = [5, 10, 20, 60]
        for period in periods:
            # 历史波动率
            factors[f'volatility_{period}'] = returns.rolling(period, min_periods=max(1, period//3)).std()
            
            # 实现波动率
            factors[f'realized_vol_{period}'] = returns.rolling(period, min_periods=max(1, period//3)).std() * np.sqrt(1440)  # 年化
            
            # 波动率排名
            factors[f'vol_rank_{period}'] = factors[f'volatility_{period}'].rolling(period*2, min_periods=period).rank() / (period*2)
        
        # True Range和ATR
        tr = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        
        for period in [14, 20, 30]:
            atr = tr.rolling(period, min_periods=max(1, period//3)).mean()
            factors[f'atr_{period}'] = atr
            factors[f'atr_ratio_{period}'] = atr / df['close']
        
        # Parkinson波动率
        for period in [10, 20]:
            parkinson_vol = np.sqrt(
                (np.log(df['high'] / df['low']) ** 2).rolling(period, min_periods=max(1, period//3)).mean() / (4 * np.log(2))
            )
            factors[f'parkinson_vol_{period}'] = parkinson_vol
        
        return factors
    
    def _calculate_technical_factors(self, df):
        """计算技术指标因子"""
        factors = {}
        
        # RSI指标
        for period in [6, 14, 21]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(period, min_periods=max(1, period//3)).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period, min_periods=max(1, period//3)).mean()
            rs = gain / (loss + 1e-8)
            factors[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # 布林带
        for period in [20, 60]:
            sma = df['close'].rolling(period, min_periods=max(1, period//3)).mean()
            std = df['close'].rolling(period, min_periods=max(1, period//3)).std()
            
            factors[f'bb_upper_{period}'] = sma + 2 * std
            factors[f'bb_lower_{period}'] = sma - 2 * std
            factors[f'bb_position_{period}'] = (df['close'] - sma) / (2 * std + 1e-8)
            factors[f'bb_width_{period}'] = (4 * std) / (sma + 1e-8)
        
        # 威廉指标
        for period in [14, 20]:
            high_max = df['high'].rolling(period, min_periods=max(1, period//3)).max()
            low_min = df['low'].rolling(period, min_periods=max(1, period//3)).min()
            factors[f'williams_r_{period}'] = (high_max - df['close']) / (high_max - low_min + 1e-8) * 100
        
        # CCI指标
        for period in [14, 20]:
            tp = (df['high'] + df['low'] + df['close']) / 3
            sma_tp = tp.rolling(period, min_periods=max(1, period//3)).mean()
            mad = tp.rolling(period, min_periods=max(1, period//3)).apply(lambda x: np.mean(np.abs(x - x.mean())))
            factors[f'cci_{period}'] = (tp - sma_tp) / (0.015 * mad + 1e-8)
        
        # Stochastic指标
        for period in [14, 21]:
            low_min = df['low'].rolling(period, min_periods=max(1, period//3)).min()
            high_max = df['high'].rolling(period, min_periods=max(1, period//3)).max()
            factors[f'stoch_k_{period}'] = (df['close'] - low_min) / (high_max - low_min + 1e-8) * 100
            factors[f'stoch_d_{period}'] = factors[f'stoch_k_{period}'].rolling(3, min_periods=1).mean()
        
        return factors
    
    def _calculate_pattern_factors(self, df):
        """计算形态因子"""
        factors = {}
        
        # 基础形态
        body_size = abs(df['close'] - df['open'])
        total_range = df['high'] - df['low'] + 1e-8
        upper_shadow = df['high'] - np.maximum(df['close'], df['open'])
        lower_shadow = np.minimum(df['close'], df['open']) - df['low']
        
        factors['body_ratio'] = body_size / total_range
        factors['upper_shadow_ratio'] = upper_shadow / total_range
        factors['lower_shadow_ratio'] = lower_shadow / total_range
        
        # K线形态
        factors['is_doji'] = (body_size / total_range < 0.1).astype(int)
        factors['is_hammer'] = (
            (lower_shadow > 2 * body_size) & 
            (upper_shadow < body_size)
        ).astype(int)
        factors['is_shooting_star'] = (
            (upper_shadow > 2 * body_size) & 
            (lower_shadow < body_size)
        ).astype(int)
        
        # 缺口
        factors['gap_up'] = (df['open'] > df['high'].shift(1)).astype(int)
        factors['gap_down'] = (df['open'] < df['low'].shift(1)).astype(int)
        factors['gap_size'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        
        # 趋势强度
        for period in [5, 10, 20]:
            # 线性回归斜率
            def calc_slope(x):
                if len(x) < 2:
                    return 0
                return np.polyfit(range(len(x)), x, 1)[0]
            
            factors[f'trend_strength_{period}'] = df['close'].rolling(period, min_periods=max(1, period//3)).apply(calc_slope)
            
            # 价格相关性
            factors[f'price_corr_{period}'] = df['close'].rolling(period, min_periods=max(1, period//3)).apply(
                lambda x: np.corrcoef(x, np.arange(len(x)))[0, 1] if len(x) > 1 else 0
            )
        
        return factors


class OptimizedDataProcessor:
    """优化的数据处理器"""
    
    def __init__(self):
        self.scaler = None
        self.factor_stats = {}
        
    def create_robust_labels(self, df):
        """创建稳健的标签"""
        print("📊 创建稳健标签...")
        
        labels = {}
        
        # 多周期收益率预测
        periods = [5, 10, 20, 30, 60]
        for period in periods:
            # 未来收益率
            future_return = df['close'].shift(-period) / df['close'] - 1
            labels[f'return_{period}min'] = future_return
            
            # 方向分类（五分类）
            quantiles = future_return.quantile([0.2, 0.4, 0.6, 0.8])
            labels[f'direction_{period}min'] = pd.cut(
                future_return,
                bins=[-np.inf, quantiles[0.2], quantiles[0.4], quantiles[0.6], quantiles[0.8], np.inf],
                labels=[0, 1, 2, 3, 4]
            ).astype(float)
            
            # 极值标签
            labels[f'extreme_up_{period}min'] = (future_return > future_return.quantile(0.9)).astype(int)
            labels[f'extreme_down_{period}min'] = (future_return < future_return.quantile(0.1)).astype(int)
        
        return pd.DataFrame(labels, index=df.index)
    
    def smart_data_cleaning(self, factors_df, labels_df):
        """智能数据清理"""
        print("🧹 智能数据清理...")
        
        # 因子质量评估
        factor_quality = {}
        for col in factors_df.columns:
            nan_ratio = factors_df[col].isnull().sum() / len(factors_df)
            inf_ratio = np.isinf(factors_df[col]).sum() / len(factors_df)
            zero_ratio = (factors_df[col] == 0).sum() / len(factors_df)
            
            quality_score = 1 - (nan_ratio + inf_ratio + zero_ratio * 0.1)
            factor_quality[col] = quality_score
        
        # 选择高质量因子
        quality_threshold = 0.7
        good_factors = [col for col, score in factor_quality.items() if score >= quality_threshold]
        
        print(f"保留 {len(good_factors)} / {len(factors_df.columns)} 个高质量因子")
        
        # 数据清理
        clean_factors = factors_df[good_factors].copy()
        
        # 智能填充
        for col in clean_factors.columns:
            if clean_factors[col].dtype in ['float64', 'int64']:
                # 用中位数填充异常值
                q1 = clean_factors[col].quantile(0.25)
                q3 = clean_factors[col].quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                clean_factors[col] = clean_factors[col].clip(lower_bound, upper_bound)
                
                # 前向填充
                clean_factors[col] = clean_factors[col].fillna(method='ffill')
                # 后向填充
                clean_factors[col] = clean_factors[col].fillna(method='bfill')
                # 最后用中位数填充
                clean_factors[col] = clean_factors[col].fillna(clean_factors[col].median())
        
        # 合并并最终清理
        combined_df = pd.concat([clean_factors, labels_df], axis=1)
        combined_df = combined_df.dropna()
        
        print(f"清理后数据: {len(combined_df)} 行")
        
        return combined_df, good_factors
    
    def create_sequences_advanced(self, features, labels, sequence_length=60):
        """创建高级序列数据"""
        print("📦 创建高级序列数据...")
        
        X, y = [], []
        timestamps = []
        
        for i in range(sequence_length, len(features)):
            # 特征序列
            feature_seq = features.iloc[i-sequence_length:i].values
            
            # 多目标标签
            label_vals = labels.iloc[i].values
            
            # 数据质量检查
            if not (np.isnan(feature_seq).any() or np.isnan(label_vals).any()):
                X.append(feature_seq)
                y.append(label_vals)
                timestamps.append(features.index[i])
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.float32)
        
        print(f"序列数据: X={X.shape}, y={y.shape}")
        
        return X, y, timestamps
    
    def time_series_split(self, X, y, timestamps, train_ratio=0.6, val_ratio=0.2):
        """时间序列分割"""
        print("📅 时间序列分割...")
        
        n = len(X)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        X_train = X[:train_end]
        y_train = y[:train_end]
        ts_train = timestamps[:train_end]
        
        X_val = X[train_end:val_end]
        y_val = y[train_end:val_end]
        ts_val = timestamps[train_end:val_end]
        
        X_test = X[val_end:]
        y_test = y[val_end:]
        ts_test = timestamps[val_end:]
        
        print(f"训练集: {len(X_train)} 样本")
        print(f"验证集: {len(X_val)} 样本")
        print(f"测试集: {len(X_test)} 样本")
        
        return (X_train, y_train, ts_train), (X_val, y_val, ts_val), (X_test, y_test, ts_test)
    
    def robust_normalization(self, X_train, X_val, X_test):
        """稳健标准化"""
        print("🔄 稳健标准化...")
        
        # 使用RobustScaler，对异常值不敏感
        n_samples, seq_len, n_features = X_train.shape
        
        # 重塑数据
        X_train_reshaped = X_train.reshape(-1, n_features)
        X_val_reshaped = X_val.reshape(-1, n_features)
        X_test_reshaped = X_test.reshape(-1, n_features)
        
        # 拟合标准化器
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train_reshaped)
        X_val_scaled = self.scaler.transform(X_val_reshaped)
        X_test_scaled = self.scaler.transform(X_test_reshaped)
        
        # 重塑回原形状
        X_train_scaled = X_train_scaled.reshape(X_train.shape)
        X_val_scaled = X_val_scaled.reshape(X_val.shape)
        X_test_scaled = X_test_scaled.reshape(X_test.shape)
        
        return X_train_scaled, X_val_scaled, X_test_scaled


class OptimizedLSTM(nn.Module):
    """优化的LSTM模型"""
    
    def __init__(self, input_size, hidden_size=128, num_layers=2, dropout=0.2, num_targets=15):
        super(OptimizedLSTM, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            batch_first=True,
            bidirectional=False
        )
        
        # 注意力层
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, num_targets)
        self.relu = nn.ReLU()
        self.batch_norm = nn.BatchNorm1d(hidden_size // 2)
        
    def forward(self, x):
        # LSTM
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 取最后一个时间步
        last_output = attn_out[:, -1, :]
        
        # 全连接层
        out = self.dropout(last_output)
        out = self.relu(self.fc1(out))
        out = self.batch_norm(out)
        out = self.dropout(out)
        out = self.fc2(out)
        
        return out


class OptimizedTrainer:
    """优化的训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        self.best_model_state = None
        
    def train_model(self, train_loader, val_loader, epochs=100, lr=0.001, patience=15):
        """训练模型"""
        print("🚀 开始模型训练...")
        
        criterion = nn.MSELoss()
        optimizer = optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=8, verbose=True
        )
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            train_count = 0
            
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
                train_count += 1
            
            # 验证阶段
            self.model.eval()
            val_loss = 0
            val_count = 0
            
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X = batch_X.to(self.device)
                    batch_y = batch_y.to(self.device)
                    
                    outputs = self.model(batch_X)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
                    val_count += 1
            
            avg_train_loss = train_loss / train_count
            avg_val_loss = val_loss / val_count
            
            self.train_losses.append(avg_train_loss)
            self.val_losses.append(avg_val_loss)
            
            scheduler.step(avg_val_loss)
            
            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                self.best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch:3d}: Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
            
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch}')
                break
        
        # 加载最佳模型
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)
        
        print("✅ 训练完成！")
        
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                outputs = self.model(batch_X)
                predictions.append(outputs.cpu().numpy())
                actuals.append(batch_y.cpu().numpy())
        
        predictions = np.vstack(predictions)
        actuals = np.vstack(actuals)
        
        return predictions, actuals


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        pass
    
    def evaluate_comprehensive(self, predictions, actuals, target_names):
        """综合评估"""
        print("\n📊 综合模型评估报告")
        print("=" * 80)
        
        results = {}
        
        for i, target in enumerate(target_names):
            pred = predictions[:, i]
            actual = actuals[:, i]
            
            # 基础指标
            mse = mean_squared_error(actual, pred)
            mae = mean_absolute_error(actual, pred)
            
            # 相关性
            corr = np.corrcoef(pred, actual)[0, 1] if len(pred) > 1 else 0
            
            # 方向准确性
            pred_direction = np.sign(pred)
            actual_direction = np.sign(actual)
            direction_accuracy = np.mean(pred_direction == actual_direction)
            
            # 信息比率
            excess_return = pred - actual
            info_ratio = np.mean(excess_return) / (np.std(excess_return) + 1e-8)
            
            # 夏普比率（如果是收益率预测）
            if 'return' in target:
                sharpe_ratio = np.mean(pred) / (np.std(pred) + 1e-8)
            else:
                sharpe_ratio = 0
            
            results[target] = {
                'mse': mse,
                'mae': mae,
                'correlation': corr,
                'direction_accuracy': direction_accuracy,
                'information_ratio': info_ratio,
                'sharpe_ratio': sharpe_ratio
            }
            
            print(f"\n{target}:")
            print(f"  MSE: {mse:.6f}")
            print(f"  MAE: {mae:.6f}")
            print(f"  相关性: {corr:.4f}")
            print(f"  方向准确性: {direction_accuracy:.2%}")
            print(f"  信息比率: {info_ratio:.4f}")
            if sharpe_ratio != 0:
                print(f"  夏普比率: {sharpe_ratio:.4f}")
        
        return results
    
    def plot_results(self, predictions, actuals, target_names, save_path='results/'):
        """绘制结果"""
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        
        # 选择几个主要目标进行可视化
        main_targets = [name for name in target_names if 'return_30min' in name or 'return_60min' in name][:4]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, target in enumerate(main_targets):
            if i >= 4:
                break
            
            target_idx = target_names.index(target)
            pred = predictions[:, target_idx]
            actual = actuals[:, target_idx]
            
            # 散点图
            axes[i].scatter(actual, pred, alpha=0.6, s=1)
            axes[i].plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'r--', lw=2)
            axes[i].set_xlabel('Actual')
            axes[i].set_ylabel('Predicted')
            axes[i].set_title(f'{target}')
            
            # 添加相关性
            corr = np.corrcoef(pred, actual)[0, 1]
            axes[i].text(0.05, 0.95, f'Corr: {corr:.3f}', transform=axes[i].transAxes, 
                        bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        plt.savefig(f'{save_path}/model_predictions.png', dpi=300, bbox_inches='tight')
        plt.show()


def main_optimized_pipeline(df):
    """主要优化流程"""
    print("🚀 启动全面优化的机器学习流程")
    print("=" * 70)
    
    # 1. 因子计算
    print("\n第一步: 优化因子计算")
    factor_engine = OptimizedFactorEngine()
    factors_df = factor_engine.calculate_all_factors(df)
    
    # 2. 数据处理
    print("\n第二步: 数据处理")
    processor = OptimizedDataProcessor()
    labels_df = processor.create_robust_labels(df)
    
    # 3. 数据清理
    print("\n第三步: 数据清理")
    combined_df, good_factors = processor.smart_data_cleaning(factors_df, labels_df)
    
    if len(combined_df) < 1000:
        print("❌ 数据量不足，无法进行有效训练")
        return None
    
    # 4. 序列创建
    print("\n第四步: 序列创建")
    features = combined_df[good_factors]
    labels = combined_df[labels_df.columns]
    
    X, y, timestamps = processor.create_sequences_advanced(features, labels, sequence_length=60)
    
    if len(X) < 500:
        print("❌ 序列数据不足")
        return None
    
    # 5. 数据分割
    print("\n第五步: 数据分割")
    (X_train, y_train, ts_train), (X_val, y_val, ts_val), (X_test, y_test, ts_test) = processor.time_series_split(X, y, timestamps)
    
    # 6. 数据标准化
    print("\n第六步: 数据标准化")
    X_train_scaled, X_val_scaled, X_test_scaled = processor.robust_normalization(X_train, X_val, X_test)
    
    # 7. 模型训练
    print("\n第七步: 模型训练")
    
    # 创建数据加载器
    batch_size = 64
    train_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_train_scaled), torch.FloatTensor(y_train)),
        batch_size=batch_size, shuffle=True
    )
    val_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_val_scaled), torch.FloatTensor(y_val)),
        batch_size=batch_size
    )
    test_loader = DataLoader(
        TensorDataset(torch.FloatTensor(X_test_scaled), torch.FloatTensor(y_test)),
        batch_size=batch_size
    )
    
    # 创建模型
    input_size = X_train_scaled.shape[2]
    num_targets = y_train.shape[1]
    
    model = OptimizedLSTM(input_size=input_size, num_targets=num_targets)
    trainer = OptimizedTrainer(model)
    
    # 训练模型
    trainer.train_model(train_loader, val_loader, epochs=100, patience=15)
    
    # 8. 预测和评估
    print("\n第八步: 预测和评估")
    predictions, actuals = trainer.predict(test_loader)
    
    # 评估
    evaluator = ModelEvaluator()
    target_names = labels_df.columns.tolist()
    results = evaluator.evaluate_comprehensive(predictions, actuals, target_names)
    
    # 可视化
    evaluator.plot_results(predictions, actuals, target_names)
    
    # 保存结果
    save_results = {
        'model_state': trainer.best_model_state,
        'predictions': predictions,
        'actuals': actuals,
        'target_names': target_names,
        'feature_names': good_factors,
        'evaluation_results': results,
        'scaler': processor.scaler
    }
    
    with open('results/optimized_model_results.pkl', 'wb') as f:
        pickle.dump(save_results, f)
    
    print("\n✅ 全面优化流程完成！")
    print(f"📊 最终结果:")
    print(f"  - 使用因子数量: {len(good_factors)}")
    print(f"  - 训练样本数: {len(X_train)}")
    print(f"  - 测试样本数: {len(X_test)}")
    print(f"  - 预测目标数: {num_targets}")
    
    return save_results


if __name__ == "__main__":
    # 测试代码
    pkl_path = 'data/BTC_USDT_1m_20250313_20250412.pkl'
    
    if os.path.exists(pkl_path):
        print(f"📁 加载数据从: {pkl_path}")
        data = pd.read_pickle(pkl_path)
        
        # 运行优化流程
        results = main_optimized_pipeline(data)
        
        if results:
            print("\n🎉 优化流程成功完成！")
            print("📁 结果已保存到: results/optimized_model_results.pkl")
        else:
            print("\n❌ 优化流程失败")
    else:
        print(f"❌ 数据文件未找到: {pkl_path}") 